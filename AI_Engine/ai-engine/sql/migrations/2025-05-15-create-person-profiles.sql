CREATE TABLE person_profiles (
    id varchar(36) PRIMARY KEY,
    primary_image_url varchar(256),
    images_metadata json DEFAULT NULL,
    first_name varchar(36) NULL,
    last_name varchar(36) NULL,
    created_at_utc timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at_utc timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    age int NOT NULL,
    gender varchar(10) NULL,
    employee_id varchar(36) NULL,
    tags json DEFAULT NULL,
    misc_info json DEFAULT NULL,
    watch_enabled tinyint(1) NOT NULL DEFAULT '0',
    is_enabled tinyint(1) NOT NULL DEFAULT '1',
    tenant_id varchar(36) NULL
);

CREATE TABLE `person_profile_location_map` (
    `person_profile_id` varchar(36) NOT NULL,
    `location_id` bigint NOT NULL,
    `is_active` int DEFAULT '1',
    `created_at_utc` timestamp NULL DEFAULT (now()),
    `updated_at_utc` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`person_profile_id`, `location_id`),
    UNIQUE KEY `person_profile_location` (`person_profile_id`, `location_id`),
    CONSTRAINT `person_profile_id_fk` FOREIGN KEY (`person_profile_id`) REFERENCES `person_profiles` (`id`),
    CONSTRAINT `location_id_fk` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`)
);

INSERT INTO
    `capabilities` (`name`, `description`)
VALUES
    (
        'person_profiles:view',
        'View all person profile and associated details'
    );

INSERT INTO
    `capabilities` (`name`, `description`)
VALUES
    (
        'person_profiles:modify',
        'Modify an existing person profile or add a new one'
    );

INSERT INTO
    `capabilities` (`name`, `description`)
VALUES
    (
        'person_profiles/mappings:modify',
        'View all person profile and associated location details'
    );

INSERT INTO
    `roles_capabilities`(`role_id`, `capability_id`)
SELECT
    `roles`.`id`,
    `capabilities`.`id`
from
    `capabilities`
    CROSS JOIN `roles`
WHERE
    `capabilities`.`name` like "person_profiles%"
    AND `roles`.`name` IN ("Hakimo Support");
