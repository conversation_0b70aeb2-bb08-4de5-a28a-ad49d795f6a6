-- Add a config column to the locations table to store configuration like twilioCallerId
ALTER TABLE locations
ADD COLUMN `config` JSON default ('{}');

-- For each location, copy the twilioCallerId from its tenant to the location's config
UPDATE locations l
JOIN tenants t ON l.tenant_id = t.id
SET l.config = JSON_OBJECT('twilioCallerId', JSON_EXTRACT(t.config, '$.twilioCallerId'))
WHERE JSON_EXTRACT(t.config, '$.twilioCallerId') IS NOT NULL;
