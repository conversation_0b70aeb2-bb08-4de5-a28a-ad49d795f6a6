-- This migration replaces the unique constraint on location_id with a composite constraint on location_id and alarm_type_id

ALTER TABLE `sop` DROP FOREIGN KEY `fk_sop_location_id`;
ALTER TABLE `sop` DROP INDEX `uniq_sop_location_id`;
ALTER TABLE `sop` ADD UNIQUE INDEX `uniq_sop_location_alarm_type` (`location_id`, `alarm_type_id`);
ALTER TABLE `sop` ADD CONSTRAINT `fk_sop_location_id` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`);
