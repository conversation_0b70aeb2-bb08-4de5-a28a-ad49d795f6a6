import json

import structlog
from flask import Blueprint, request

import controller as ctrl

from .endpoint import PersonProfileEndpoint

log = structlog.get_logger("hakimo", module="person_profile_api")


def person_profile_api(controller: ctrl.ControllerMap):
    api = Blueprint("person_profile_api", __name__)
    person_profile_endpoint = PersonProfileEndpoint(controller)

    @api.route("", methods=["GET"])
    def get_person_profiles():
        """
        Get Person Profiles
        ---
        description: Get Person Profiles based on query parameters
        tags:
          - Person Profiles
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token for authorization
          - name: tenant_ids
            in: query
            schema:
              type: string
            required: false
            description: Tenant IDs to filter by
          - name: locationIds
            in: query
            schema:
              type: string
            required: false
            description: Location Id filter for the Person Profiles
          - name: page
            in: query
            schema:
              type: int
            required: false
            description: Page number
          - name: pageSize
            in: query
            schema:
              type: int
            required: false
            description: Number of elements per page
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/PersonProfilesDTO'
          400:
            description: Bad Request
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/BadRequest'
          404:
            description: Not Found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/NotFoundResponse'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        return person_profile_endpoint.get_person_profiles(
            query=request.args.to_dict()
        )

    @api.route("/<person_profile_id>", methods=["PATCH"])
    def update_person_profile(person_profile_id):
        """
        Update Person Profiles
        ---
        description: Update Person Profiles
        tags:
          - Person Profiles
        requestBody:
          description: Request body for updating Person Profiles
          required: true
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpsertPersonProfileRequest'
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token authorization for the API
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/OkResponse'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        payload = json.loads(request.data)
        return person_profile_endpoint.update_person_profile(
            person_profile_id, payload
        )

    @api.route("", methods=["POST"])
    def add_person_profiles():
        """
        Add Person Profiles
        ---
        description: Add Person Profiles
        tags:
          - Person Profiles
        requestBody:
          description: Request body for updating Person Profiles
          required: true
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpsertPersonProfileRequest'
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token authorization for the API
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/OkResponse'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        payload = json.loads(request.data)
        return person_profile_endpoint.add_person_profiles(payload)

    @api.route("/mapping", methods=["POST", "DELETE"])
    def update_person_profile_location_mapping():
        """
        Update Person Profile Location mappings
        ---
        description: Either add or delete Person Profile Location mappings
        tags:
          - Person Profiles
        requestBody:
          description: Request body for updating Person Profile Location mapping
          required: true
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdatePersonProfileRequest'
        parameters:
          - name: Authorization
            in: header
            schema:
              type: string
            required: true
            description: Bearer token authorization for the API
        responses:
          200:
            description: Request Processed Successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/OkResponse'
          500:
            description: Internal Server Error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/InternalServerError'
        """
        payload = json.loads(request.data)
        if request.method == "POST":
            return person_profile_endpoint.mapping("ADD", payload)
        elif request.method == "DELETE":
            return person_profile_endpoint.mapping("DELETE", payload)
        else:
            return person_profile_endpoint.mapping("UNKNOWN", payload)

    @api.route("/<person_profile_id>/upload_image", methods=["PUT"])
    def upload_person_profile_image(
        person_profile_id,
    ):  # pylint: disable=unused-variable
        return person_profile_endpoint.upload_person_profile_image(
            person_profile_id,
            request.files,
            request.args.to_dict(),
        )

    return api
