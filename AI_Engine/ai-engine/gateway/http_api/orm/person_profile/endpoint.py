import os.path as osp
import tempfile
import time
import typing
import uuid
from http import HTTPStatus

import structlog
from boto3.exceptions import Boto3Error
from werkzeug.datastructures import FileStorage

import controller as ctrl
import errors
from common_utils.image_utils import check_image_valid
from common_utils.s3_utils import (
    S3_BUCKET_NAME_KEY,
    S3_FILE_NAME_KEY,
    construct_S3_file_name,
    get_s3_path_details,
    get_signed_s3_url,
    upload_file_to_s3,
)
from common_utils.string_helpers import replace_special_char_and_space
from config import backend_config as config
from gateway.common.endpoint_guard import guard_endpoint
from gateway.http_api.orm.constants import QUERY_SEPARATOR
from gateway.http_api.orm.user.utils import get_user_tenant_ids
from gateway.http_api.orm.utils import get_page_params, parse_filter_str
from models_rds.person_profile import PersonProfiles
from models_rds.users import Users

log = structlog.get_logger("hakimo", module="PersonProfileEndpoint")


class PersonProfileEndpoint:
    def __init__(self, controller: ctrl.ControllerMap):
        self.controller = controller

    @guard_endpoint(["person_profiles:view"])
    def get_person_profiles(
        self,
        user: typing.Optional[Users] = None,
        query: typing.Optional[typing.Dict[str, typing.Any]] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None
        try:
            query_dict = _unmarshal_get_person_profile_query(query)
        except (ValueError, KeyError):
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Bad query params"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        tenant_ids = [user.tenant_id]
        if user.msp_tenants:
            tenant_ids = user.msp_tenants
        if query_dict.get("tenant_ids") is None:
            query_dict["tenant_ids"] = tenant_ids
        elif query_dict["tenant_ids"]:
            for tenant in query_dict["tenant_ids"]:
                if tenant not in tenant_ids:
                    api_resp["status"] = HTTPStatus.FORBIDDEN.value
                    api_resp["message"] = "Bad query params"
                    return (api_resp, HTTPStatus.FORBIDDEN.value)
        if query_dict.get("location_ids") is not None:
            query_dict["location_ids"] = [
                int(location_id) for location_id in query_dict["location_ids"]
            ]
            person_profiles, count = (
                self.controller.person_profile_location_map.get_person_profiles_for_location(
                    **query_dict
                )
            )
        else:
            person_profiles, count = (
                self.controller.person_profile.get_person_profiles(
                    **query_dict
                )
            )
        for profie in person_profiles:
            if profie.tenant_id not in tenant_ids:
                api_resp["status"] = HTTPStatus.FORBIDDEN.value
                api_resp["message"] = "Bad query params"
                return (api_resp, HTTPStatus.FORBIDDEN.value)
        api_resp["payload"] = {
            "items": _marshal_person_profiles(person_profiles),
            "total": count,
        }
        api_resp["message"] = "Processed Successfully"
        api_resp["status"] = HTTPStatus.OK.value
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["person_profiles:modify"])
    def add_person_profiles(
        self,
        update_payload: typing.Sequence[typing.Dict],
        user: typing.Optional[Users] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None
        try:
            upsert_payload = _unmarshal_upsert_person_profile_payload(
                update_payload, user
            )
        except ValueError as ve:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = str(ve)
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        except KeyError:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Missing required keys in the payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        except TypeError:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Type mismatch in payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        user_tenant_ids = get_user_tenant_ids(user)
        # Make sure profile is added to tenant that user is authorized for
        tenant_id = str(update_payload[0].get("tenantId"))
        if tenant_id and tenant_id not in user_tenant_ids:
            api_resp["status"] = HTTPStatus.FORBIDDEN.value
            api_resp["message"] = (
                "user tenant is different from profile tenant"
            )
            return (api_resp, HTTPStatus.FORBIDDEN.value)
        self.controller.person_profile.upsert_person_profiles(upsert_payload)
        api_resp["message"] = "Processed Successfully"
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["personProfileId"] = upsert_payload[0].id
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["person_profiles:modify"])
    def update_person_profile(
        self,
        person_profile_id: str,
        update_payload: typing.Dict,
        user: typing.Optional[Users] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert person_profile_id is not None
        assert user is not None
        try:
            update_payload["id"] = person_profile_id
            update_profie_payload = _unmarshal_person_profile(update_payload)
        except ValueError as ve:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = str(ve)
            return (api_resp, HTTPStatus.BAD_REQUEST.value)

        user_tenant_ids = get_user_tenant_ids(user)

        if (
            update_profie_payload.tenant_id is not None
            and update_profie_payload.tenant_id not in user_tenant_ids
        ):
            api_resp["status"] = HTTPStatus.FORBIDDEN.value
            api_resp["message"] = "Bad upsert payload"
            return (api_resp, HTTPStatus.FORBIDDEN.value)
        existing_profie = (
            self.controller.person_profile.get_person_profile_by_id(
                person_profile_id=update_profie_payload.id
            )
        )
        if not existing_profie:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Person profile does not exist"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        if (
            update_profie_payload.tenant_id
            and update_profie_payload.tenant_id not in user_tenant_ids
        ) or existing_profie.tenant_id not in user_tenant_ids:
            api_resp["status"] = HTTPStatus.FORBIDDEN.value
            api_resp["message"] = (
                "user tenant is different from profile tenant"
            )
            return (api_resp, HTTPStatus.FORBIDDEN.value)
        self.controller.person_profile.upsert_person_profiles(
            [update_profie_payload]
        )
        api_resp["message"] = "Processed Successfully"
        api_resp["status"] = HTTPStatus.OK.value
        api_resp["personProfileId"] = update_profie_payload.id
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["person_profiles/mappings:modify"])
    def mapping(
        self,
        action: str,
        query_params: typing.Dict[str, typing.Union[str, int]],
        user: typing.Optional[Users] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None
        try:
            mapping_payload = _unmarshal_person_profile_loc_mapping_payload(
                query_params
            )
        except (ValueError, KeyError, TypeError):
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Bad upsert payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        tenant_ids = [user.tenant_id]
        if user.msp_tenants:
            tenant_ids.extend(user.msp_tenants)
        existing_profie, count = (
            self.controller.person_profile.get_person_profiles(
                person_profile_id=mapping_payload["person_profile_id"]
            )
        )
        existing_location = self.controller.locations.get_location_by_id(
            location_id=mapping_payload["location_id"]
        )
        if existing_location is None or not existing_profie:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Bad upsert payload"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        if (
            existing_profie[0].tenant_id not in tenant_ids
            or existing_location.tenant_id not in tenant_ids
        ):
            api_resp["status"] = HTTPStatus.FORBIDDEN.value
            api_resp["message"] = f"Cannot {action.lower()} mapping"
            return (api_resp, HTTPStatus.FORBIDDEN.value)
        if action.lower() == "add":
            try:
                self.controller.person_profile_location_map.add_person_profiles_location_mapping(
                    mapping_payload["person_profile_id"],
                    mapping_payload["location_id"],
                )
            except errors.EntityExistsError:
                api_resp["status"] = HTTPStatus.BAD_REQUEST.value
                api_resp["message"] = "Mapping already exists"
                return (api_resp, HTTPStatus.BAD_REQUEST.value)
        elif action.lower() == "delete":
            self.controller.person_profile_location_map.delete_person_profiles_location_mapping(
                mapping_payload["person_profile_id"],
                mapping_payload["location_id"],
            )
        else:
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Mapping already exists"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        api_resp["message"] = "Processed Successfully"
        api_resp["status"] = HTTPStatus.OK.value
        return api_resp, HTTPStatus.OK.value

    @guard_endpoint(["person_profiles:modify"])
    def upload_person_profile_image(
        self,
        person_profile_id: str,
        files: typing.Dict,
        update_payload: typing.Dict,
        user: typing.Optional[Users] = None,
    ):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert user is not None
        if files.get("profileImage") is None:
            log.error(
                "No profile image file attached.",
                person_profile_id=person_profile_id,
            )
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = (
                "Invalid payload. No profile image file attached."
            )
            return api_resp, HTTPStatus.BAD_REQUEST.value

        profile_image = files["profileImage"]

        if not profile_image.filename:
            log.error(
                f"Profile Image file must have a name. person_profile_id: {person_profile_id}"
            )
            api_resp["message"] = "File must have a name"
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            return api_resp, HTTPStatus.BAD_REQUEST.value

        (base_file_name, ext) = osp.splitext(profile_image.filename)

        updated_file_name = replace_special_char_and_space(base_file_name)

        profile_image.filename = f"{updated_file_name}{ext}"

        person_profile = (
            self.controller.person_profile.get_person_profile_by_id(
                person_profile_id=person_profile_id
            )
        )

        if person_profile is None:
            api_resp["message"] = "Invalid person profile passed"
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            return api_resp, HTTPStatus.BAD_REQUEST.value

        conf = config.gateway()
        s3_bucket = conf["s3Bucket"]
        tenant_id = person_profile.tenant_id

        if not check_image_valid(profile_image):
            api_resp["message"] = (
                "Invalid person profile image file passed. Please upload a image file"
            )
            api_resp["status"] = HTTPStatus.FORBIDDEN.value
            return api_resp, HTTPStatus.FORBIDDEN.value

        isPrimary = update_payload.get("isPrimary", "false")
        image_type = "primary" if isPrimary == "true" else "secondary"
        image_name = (
            person_profile_id
            if isPrimary == "true"
            else f"{person_profile_id}_{int(time.time() * 1000)}"
        )
        s3_file_name = upload_profile_image_to_s3(
            tenant_id,
            person_profile_id,
            image_type,
            image_name,
            ext,
            profile_image,
            s3_bucket,
        )

        if s3_file_name is None:
            api_resp["status"] = HTTPStatus.INTERNAL_SERVER_ERROR.value
            api_resp["message"] = (
                "Failed to upload person profile image file to S3."
            )
            return api_resp, HTTPStatus.INTERNAL_SERVER_ERROR.value

        s3_file_object_url = f"https://{s3_bucket}.s3.{config.HAIE.AWS_S3_REGION}.amazonaws.com/{s3_file_name}"

        try:
            if image_type == "primary":
                person_profile.primary_image_url = s3_file_object_url
            else:
                if person_profile.images_metadata is None:
                    person_profile.images_metadata = []
                person_profile.images_metadata.append(s3_file_object_url)
            self.controller.person_profile.upsert_person_profiles(
                [person_profile]
            )

        except ValueError:
            api_resp["message"] = "Failed to upload person profile image"
            api_resp["status"] = HTTPStatus.INTERNAL_SERVER_ERROR.value
            return api_resp, HTTPStatus.INTERNAL_SERVER_ERROR.value

        api_resp["status"] = HTTPStatus.OK.value
        api_resp["message"] = "Person profile image updated successfully."
        api_resp["personProfileId"] = person_profile_id
        api_resp["uploadedImageURL"] = s3_file_object_url
        return api_resp, HTTPStatus.OK.value


def _unmarshal_person_profile_loc_mapping_payload(
    query_params: typing.Dict[str, typing.Union[str, int]],
) -> typing.Dict[str, typing.Any]:
    return {
        "person_profile_id": query_params["personProfileId"],
        "location_id": query_params["locationId"],
    }


def _unmarshal_person_profile(person_profile: typing.Dict):
    profie = PersonProfiles()
    if person_profile.get("id") is not None:
        profie.id = person_profile["id"]
    if person_profile.get("tenantId"):
        profie.tenant_id = person_profile["tenantId"]
    if person_profile.get("firstName") is not None:
        profie.first_name = person_profile["firstName"]
    if person_profile.get("lastName") is not None:
        profie.last_name = person_profile["lastName"]
    if person_profile.get("isEnabled") is not None:
        profie.is_enabled = person_profile["isEnabled"]
    if person_profile.get("age") is not None:
        profie.age = person_profile["age"]
    if person_profile.get("primaryImageUrl") is not None:
        profie.primary_image_url = person_profile["primaryImageUrl"]
    if person_profile.get("gender") is not None:
        profie.gender = person_profile["gender"]
    if person_profile.get("employeeId") is not None:
        profie.employee_id = person_profile["employeeId"]
    if person_profile.get("imagesMetadat") is not None:
        profie.images_metadata = person_profile["imagesMetadat"]
    if person_profile.get("miscInfo") is not None:
        profie.misc_info = person_profile["miscInfo"]
    if person_profile.get("tags") is not None:
        profie.tags = person_profile["tags"]
    if person_profile.get("watchEnabled") is not None:
        profie.watch_enabled = person_profile["watchEnabled"]
    if profie.id is None and (
        profie.tenant_id is None or profie.primary_image_url is None
    ):
        raise ValueError("Bad upsert payload")
    if profie.id is None:
        profie.id = str(uuid.uuid4())
    return profie


def _unmarshal_upsert_person_profile_payload(
    update_payload: typing.Sequence[typing.Dict], user: Users
) -> typing.List[PersonProfiles]:
    ret = []
    for payload in update_payload:
        profie = _unmarshal_person_profile(payload)
        ret.append(profie)
    return ret


def _marshal_person_profiles(
    person_profiles: typing.Sequence[PersonProfiles],
) -> typing.List[typing.Dict]:
    ret = []
    for person_profile in person_profiles:
        ret.append(_marshal_single_profie(person_profile))
    return ret


def _marshal_single_profie(
    person_profile: PersonProfiles,
) -> typing.Dict:
    signed_primary_profile_image = None
    if person_profile.primary_image_url is not None:
        s3_details = get_s3_path_details(person_profile.primary_image_url)
        if s3_details is not None:
            signed_primary_profile_image = get_signed_s3_url(
                s3_details[S3_BUCKET_NAME_KEY],
                s3_details[S3_FILE_NAME_KEY],
                expiration_secs=30,
            )
    return {
        "id": person_profile.id,
        "tenantId": person_profile.tenant_id,
        "primaryImageURL": signed_primary_profile_image,
        "watchEnabled": person_profile.watch_enabled,
        "employeeId": person_profile.employee_id,
        "isEnabled": person_profile.is_enabled,
        "firstName": person_profile.first_name,
        "lastName": person_profile.last_name,
        "miscInfo": person_profile.misc_info,
        "tags": person_profile.tags,
        "age": person_profile.age,
        "imagesMetadata": person_profile.images_metadata,
        "gender": person_profile.gender,
    }


def _unmarshal_get_person_profile_query(
    query_dict: typing.Optional[typing.Dict[str, typing.Any]],
) -> typing.Dict[str, typing.Any]:
    ret: typing.Dict[str, typing.Any] = {}
    if query_dict is None:
        return ret
    if tenant_str := query_dict.get("tenant_ids"):
        ret["tenant_ids"] = parse_filter_str(tenant_str)
    if query_dict.get("locationIds") is not None:
        ret["location_ids"] = query_dict["locationIds"].split(QUERY_SEPARATOR)
    if query_dict.get("personProfileId") is not None:
        ret["person_profile_id"] = query_dict["personProfileId"].split(
            QUERY_SEPARATOR
        )
    if is_enabled := query_dict.get("isEnabled"):
        if is_enabled.lower() == "true":
            ret["is_enabled"] = True
        elif is_enabled.lower() == "false":
            ret["is_enabled"] = False
        else:
            raise ValueError("Invalid value for isEnabled")
    ret.update(get_page_params(query_dict))
    return ret


def upload_profile_image_to_s3(
    tenant_id: str,
    profile_id: str,
    image_type: str,
    image_name: str,
    ext: str,
    profile_image_file: FileStorage,
    s3_bucket: str,
):
    with tempfile.TemporaryDirectory() as tmpdir:
        constructed_file_name = (
            f"{tenant_id}+person_profile_image+{image_type}+{image_name}{ext}"
        )
        local_profile_imag_path = osp.join(tmpdir, constructed_file_name)
        profile_image_file.save(local_profile_imag_path)
        s3_file_name = construct_S3_file_name(constructed_file_name)
        try:
            upload_file_to_s3(s3_bucket, s3_file_name, local_profile_imag_path)
        except Boto3Error as be:
            log.error(
                "Failed to upload person profile file to S3 bucket.",
                tenantId=tenant_id,
                profile_id=profile_id,
                exc_info=be,
            )
            return None
    return s3_file_name
