import datetime
import math
import typing

import pytz
import structlog

import controller as ctrl
from common_utils.s3_utils import (
    S3_BUCKET_NAME_KEY,
    S3_FILE_NAME_KEY,
    get_s3_path_details,
    get_signed_s3_url,
)
from common_utils.time_utils import convert_utc_to_tz
from config import backend_config as config
from interfaces.alarm_mapper import AlarmMapper
from interfaces.entities.entity import Entity
from models_rds.alarm_media import AlarmMedia
from models_rds.raw_alarms import RawAlarms

from .video_markers import get_alarm_video_markers

log = structlog.get_logger("hakimo.orm.alarm", module="alarms_api")


def get_alarm_local_time(
    alarm: RawAlarms, controller: ctrl.ControllerMap
) -> typing.Optional[datetime.datetime]:
    """Method to get local alarm time for alarm.
    - If corresponding column in table is populated, return column value
    - If door exists, and door has a timezone, convert time and return
    - Else, return None (unknown local time)
    """
    if alarm.alarm_timestamp_local is not None:
        return alarm.alarm_timestamp_local
    if alarm.door_id is None:
        return None
    door = controller.door.get_door(alarm.door_id)
    if door is None or door.timezone is None:
        return None
    return convert_utc_to_tz(alarm.alarm_timestamp_utc, door.timezone)


def get_alarm_media_metadata(
    alarm: RawAlarms, cm: ctrl.ControllerMap, alarm_mapper: AlarmMapper
) -> typing.Dict[str, typing.Any]:
    ret: typing.Dict[str, typing.Any] = {}
    video_markers = get_alarm_video_markers(alarm, cm, alarm_mapper)
    ret["video_markers"] = video_markers
    local_time = get_alarm_local_time(alarm, cm)
    ret["alarm_local_time"] = (
        local_time.strftime("%Y-%m-%d %H:%M:%S") if local_time else local_time
    )
    ret["video_start_timestamp"] = (
        alarm.video_start_timestamp_utc.replace(tzinfo=pytz.utc).isoformat()
        if alarm.video_start_timestamp_utc
        else None
    )
    ret["video_end_timestamp"] = (
        alarm.video_end_timestamp_utc.replace(tzinfo=pytz.utc).isoformat()
        if alarm.video_end_timestamp_utc
        else None
    )
    return ret


def get_alarm_media_details(
    alarm: RawAlarms,
    cm: ctrl.ControllerMap,
    alarm_mapper: AlarmMapper,
) -> typing.Dict:
    ret: typing.Dict[str, typing.Any] = {"alarm_id": alarm.uuid}
    ret.update(get_alarm_media_metadata(alarm, cm, alarm_mapper))
    alarm_media = cm.alarm_media.get_alarm_media(alarm.uuid)
    ret = add_media_info(ret, alarm_media)
    return ret


def add_media_info(
    ret: typing.Dict, alarm_media: typing.Sequence[AlarmMedia]
) -> typing.Dict:
    """Alarm media must be sorted from most recent to least!"""
    ret["image_urls"] = []
    ret["video_url"] = None
    ost = config.gateway()["OBJECT_STORAGE"]
    object_storage_type = "s3"
    if ost:
        object_storage_type = ost["type"]
    for media in alarm_media:
        # Currently only return latest video or latest image
        if ret["video_url"] is not None or ret["image_urls"]:
            break
        if object_storage_type == "local":
            log.debug("Object storage type", type=object_storage_type)
            set_media_type(ret, media, media.media_url)
            continue
        s3_details = get_s3_path_details(media.media_url)
        assert s3_details is not None, "Invalid media URL"
        signed_url = get_signed_s3_url(
            s3_details[S3_BUCKET_NAME_KEY], s3_details[S3_FILE_NAME_KEY], 600
        )
        set_media_type(ret, media, signed_url)
    return ret


def get_box_around_entities(entities: typing.Sequence[Entity]):
    entity_boxes = []
    for entity in entities:
        if (enclosing_box := entity.get_box_around_track()) is not None:
            entity_boxes.append(enclosing_box)
    if entity_boxes:
        x1, y1, x2, y2 = math.inf, math.inf, -math.inf, -math.inf
        for box in entity_boxes:
            x1 = min(x1, box[0])
            y1 = min(y1, box[1])
            x2 = max(x2, box[0] + box[2])
            y2 = max(y2, box[1] + box[3])
        return [x1, y1, x2 - x1, y2 - y1]
    else:
        return None


def set_media_type(ret: typing.Dict, media: AlarmMedia, signed_url: str):
    if media.media_type == "image":
        ret["image_urls"].append(signed_url)
    elif media.media_type == "video":
        ret["video_url"] = signed_url
