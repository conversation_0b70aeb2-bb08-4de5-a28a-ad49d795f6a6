import typing
from http import H<PERSON><PERSON>tatus

import structlog

import controller as ctrl
from gateway.common.endpoint_guard import guard_endpoint
from models_rds.ai_outputs_location_alarms import AIOutputsLocationAlarms
from models_rds.users import Users

log = structlog.get_logger(
    "hakimo.orm.alarm", module="AI outputs location alarm endpoint"
)


class AIOutputsLocationAlarmGetter:
    def __init__(self, controller: ctrl.ControllerMap):
        self._ctrl_map = controller

    def _is_llm_enabled_for_tenant(self, tenant_id: str) -> bool:
        tenant_obj = self._ctrl_map.tenant.get_tenant_object(tenant_id)
        if tenant_obj is None:
            return False
        alarm_processing_config = tenant_obj.alarmProcessingConfig
        if alarm_processing_config is None:
            return False

        llm_alarm_analyzer_config = alarm_processing_config.get(
            "llmAlarmAnalyzerConfig", {}
        )
        llm_event_analyzer_config = alarm_processing_config.get(
            "llmEventAnalyzerConfig", {}
        )
        llm_alarm_analyzer_enabled = (
            llm_alarm_analyzer_config.get("enabled", False)
            if isinstance(llm_alarm_analyzer_config, dict)
            else False
        )
        llm_event_analyzer_enabled = (
            llm_event_analyzer_config.get("enabled", False)
            if isinstance(llm_event_analyzer_config, dict)
            else False
        )
        is_enabled = llm_alarm_analyzer_enabled or llm_event_analyzer_enabled
        return is_enabled

    @guard_endpoint(["ai_output_location_alarm:view"])
    def _get_ai_outputs_location_alarms(
        self,
        alarm_id: typing.Optional[str],
        camera_group_id: typing.Optional[str],
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        ai_outputs: typing.List[AIOutputsLocationAlarms] = []

        try:
            tenant_id = None
            if alarm_id:
                location_alarm_id = (
                    int(alarm_id)
                    if isinstance(alarm_id, str) and alarm_id.isdigit()
                    else alarm_id
                )
                location_alarm = (
                    self._ctrl_map.location_alarms.get_location_alarm_by_id(
                        location_alarm_id
                    )
                )
                if location_alarm is not None:
                    location = self._ctrl_map.locations.get_location_by_id(
                        location_alarm.location_id
                    )
                    if location is not None:
                        tenant_id = location.tenant_id
            if camera_group_id is not None:
                # get tenant_id for camera_group
                try:
                    camera_group = (
                        self._ctrl_map.camera.get_camera_group_details(
                            camera_group_id
                        )
                    )
                    if camera_group is None:
                        api_resp["payload"] = {}
                        api_resp["status"] = HTTPStatus.NOT_FOUND.value
                        api_resp["message"] = "Camera group id is invalid"
                        return api_resp, HTTPStatus.NOT_FOUND.value
                    tenant_id = camera_group.tenant_id
                except Exception:
                    api_resp["payload"] = {}
                    api_resp["status"] = HTTPStatus.NOT_FOUND.value
                    api_resp["message"] = "Camera group id is invalid"
                    return api_resp, HTTPStatus.NOT_FOUND.value

            if tenant_id is None:
                api_resp["payload"] = {}
                api_resp["status"] = HTTPStatus.NOT_FOUND.value
                api_resp["message"] = (
                    "Could not determine tenant for the given alarm"
                )
                return api_resp, HTTPStatus.NOT_FOUND.value
            if not self._is_llm_enabled_for_tenant(tenant_id):
                api_resp["payload"] = {}
                api_resp["status"] = HTTPStatus.NOT_FOUND.value
                api_resp["message"] = (
                    "AI output for the given alarm does not exist."
                )
                return api_resp, HTTPStatus.NOT_FOUND.value

            if alarm_id:
                ai_output = self._ctrl_map._ai_outputs.get_latest_ai_output_for_location_alarm(
                    alarm_id
                )
                if ai_output is not None:
                    ai_outputs.append(ai_output)
            if camera_group_id:
                # temporary fix until pull model. Expecting camera_group_id instead of alarm if
                #  TODO: update it and use alarm group id only.
                ai_output_cam_group = self._ctrl_map._ai_outputs.get_latest_ai_output_for_camera_group_id(
                    camera_group_id
                )
                if ai_output_cam_group is not None:
                    ai_outputs.extend(ai_output_cam_group)

            if not ai_outputs:
                api_resp["payload"] = {}
                api_resp["status"] = HTTPStatus.NOT_FOUND.value
                api_resp["message"] = (
                    "AI output for the given alarm does not exist."
                )
                return api_resp, HTTPStatus.NOT_FOUND.value

            recommendations = []
            for ai_recom in ai_outputs:
                if not ai_recom.summary:
                    ai_recom.summary = ""
                if not ai_recom.recommendation:
                    ai_recom.recommendation = ""
                recommendations.append(
                    {
                        "summary": ai_recom.summary,
                        "recommendations": ai_recom.recommendation,
                        "alarmGroupId": ai_recom.alarm_group_id,
                        "createdAtUTC": ai_recom.created_at_utc,
                        "updatedAtUTC": ai_recom.updated_at_utc,
                        "locationAlarmId": ai_recom.location_alarm_id,
                    }
                )

            api_resp["payload"] = recommendations
            api_resp["status"] = HTTPStatus.OK.value
            api_resp["message"] = "Processed successfully"
            return api_resp, HTTPStatus.OK.value
        except Exception as e:
            log.error(
                "Error retrieving AI outputs for location alarm",
                error=str(e),
                stack_info=True,
            )
            api_resp["message"] = str(e)
            api_resp["status"] = HTTPStatus.INTERNAL_SERVER_ERROR.value
            return api_resp, HTTPStatus.INTERNAL_SERVER_ERROR.value
