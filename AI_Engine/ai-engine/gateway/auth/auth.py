import re

import structlog
from flask import request
from structlog.contextvars import bind_contextvars, unbind_contextvars
from werkzeug.wrappers import Request

from common_utils.rpc import RpcStatus
from config import backend_config as config
from gateway.http_errors import GatewayEx

from .auth0_token import validate_token_auth as auth0_token_validation
from .local_token import validate_token_auth as local_token_validation

log = structlog.get_logger("hakimo", module="gateway/auth")


class AuthMiddleware:
    def __init__(self, app):
        self.app = app

    def __call__(self, environ, start_response):
        path = environ["PATH_INFO"]
        auth_paths = any(
            [
                path.startswith("/api"),
                path.startswith("/orm"),
            ]
        )
        environ["Source-System"] = environ.get("HTTP_SOURCE_SYSTEM", "UNKNOWN")
        token_auth_paths = any(
            [path.startswith("/v2/orm"), path.startswith("/voice")]
        )

        # No auth paths using flassger for Swagger UI Docs
        # Or login endpoint
        no_auth_paths = any(
            [
                path.startswith("/apidocs"),
                path.startswith("/apispec"),
                path.startswith("/auth"),
            ]
        )
        if no_auth_paths:
            return self.app(environ, start_response)

        if not auth_paths and not token_auth_paths:
            # no authentication
            return self.app(environ, start_response)
        if token_auth_paths:
            return self.app(environ, start_response)

        req = Request(environ)
        tid = self.parse_tenant(req)
        bind_contextvars(tenant_id=tid)

        try:
            environ["tenant_id"] = tid
            return self.app(environ, start_response)

        finally:
            unbind_contextvars("tenant_id")

    @staticmethod
    def parse_tenant(req) -> str:
        subject = req.headers.get("ssl-client-subject-dn") or req.headers.get(
            "X-Amzn-Mtls-Clientcert-Subject"
        )
        if not subject:
            tenant_id = req.headers.get("X-TENANT-ID")
            if not tenant_id:
                raise GatewayEx(
                    RpcStatus.UNAUTHENTICATED,
                    "Invalid Request, missing tenant-id",
                )

            log.debug("ssl-client-subject-dn missing, fallback to x-tenant-id")
            return tenant_id

        tenant_match = re.search(r"(?<=CN=)[\w\s\-]*", subject)
        if not tenant_match:
            log.error("bad ssl header", header=subject)
            raise GatewayEx(
                RpcStatus.UNAUTHENTICATED,
                "Bad certificate. Most likely missing CN=",
            )

        tenant_id = tenant_match.group()

        if not tenant_id:
            raise GatewayEx(
                RpcStatus.UNAUTHENTICATED,
                "failed to parse certificate. CN is empty",
            )

        return tenant_id


def tenant_header():
    return request.environ["tenant_id"]


def get_source_system():
    source_system = request.headers.get("Source-System", "UNKNOWN")
    if source_system == "UNKNOWN":
        log.warning(
            "Source system not specified in request!",
            headers=request.headers,
        )
    return source_system


def user_email():
    return "<EMAIL>"
    # auth = config.gateway().get("authType", "auth0")
    # if auth == "auth0":
    #     token_payload = auth0_token_validation()
    # elif auth == "local":
    #     token_payload = local_token_validation()
    # else:
    #     log.exception("Unknown ORM Authentication", type=auth)
    #     raise ValueError("Invalid orm authentication type")
    # return token_payload["http://hakimo.ai/email"]
