# Used for deploying a connector to connect to redis being deployed in AWS Elastic cache prod cluster.
apiVersion: v1
kind: Service
metadata:
  name: redis-aws-prod
#  namespace: prod
spec:
  selector:
    deploymentPod: "redis-aws-prod"
  ports:
    - protocol: TCP
      port: 6379
      targetPort: redis-aws
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-aws-prod-connector
#  namespace: prod
  labels:
    deploymentPod: redis-aws-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      deploymentPod: redis-aws-prod
  template:
    metadata:
      labels:
        deploymentPod: redis-aws-prod
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - amd64
      containers:
        - name: redis-forward
          image: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-util/alpine/socat:latest
          args: [
              "tcp-listen:6379,fork,reuseaddr",
              # Change this incase RDS IP changes
              "tcp-connect:hakimo-prod-redis.og9q14.ng.0001.usw2.cache.amazonaws.com:6379",
            ]
          ports:
            - containerPort: 6379
              name: redis-aws
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 50Mi
