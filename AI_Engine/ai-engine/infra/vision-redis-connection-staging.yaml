# Used for deploying a connector to connect to redis being used on the Kapstan Staging cluster.
# Use the connectors namespace on the Kapstan Staging cluster
# This is
apiVersion: v1
kind: Service
metadata:
  name: vision-redis-staging
#  namespace: staging
spec:
  selector:
    deploymentPod: "vision-redis-staging"
  ports:
    - protocol: TCP
      port: 6379
      targetPort: vision-redis
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vision-redis-staging-connector
#  namespace: staging
  labels:
    deploymentPod: vision-redis-staging
spec:
  replicas: 1
  selector:
    matchLabels:
      deploymentPod: vision-redis-staging
  template:
    metadata:
      labels:
        deploymentPod: vision-redis-staging
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: kubernetes.io/arch
                    operator: In
                    values:
                      - amd64
      containers:
        - name: redis-forward
          image: 695273141991.dkr.ecr.us-west-2.amazonaws.com/hakimo-util/alpine/socat:latest
          args: [
              "tcp-listen:6379,fork,reuseaddr",
              # Change this incase RDS IP changes
              "tcp-connect:stagingvisionredis.og9q14.ng.0001.usw2.cache.amazonaws.com:6379",
            ]
          ports:
            - containerPort: 6379
              name: vision-redis
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 50Mi
