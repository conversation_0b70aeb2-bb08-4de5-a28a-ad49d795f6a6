import json
from typing import Dict

import boto3
import structlog

from common_utils.io_helpers import read_file
from config import backend_config as config

logger = structlog.get_logger(
    "hakimo.etl.vision_insight", module="TenantMappingExtractor"
)


class TenantMappingExtractor:
    """
    Extracts tenant mapping configuration from a JSON file stored in S3.
    Expects a JSON dictionary { "tenant_mappings": { "premise": "tenant_name" } } in the S3 file.
    """

    def __init__(self, bucket_name: str, region: str):
        """
        Initializes the extractor with S3 details.
        """
        self.bucket_name = bucket_name
        self.region = region
        self.key = "insight/tenant-mapping.json"
        self.access_key = read_file(config.HAIE.AWS_ACCESS_KEY_ID, missing="")
        self.secret_key = read_file(config.HAIE.AWS_SECRET_KEY, missing="")

    def extract(self) -> Dict[str, str]:
        """
        Loads and processes the tenant mapping from the specified S3 location.
        """
        try:
            logger.info(
                "Attempting to load tenant mapping from S3",
                bucket=self.bucket_name,
                key=self.key,
            )
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=self.access_key,
                aws_secret_access_key=self.secret_key,
                region_name=self.region,
            )
            response = s3_client.get_object(
                Bucket=self.bucket_name, Key=self.key
            )
            # mapping content is like {"tenant_mappings":{"premise":"tenant_name"}}
            mapping_content = response["Body"].read().decode("utf-8")
            raw_mapping = json.loads(mapping_content)

            if (
                not isinstance(raw_mapping, dict)
                or "tenant_mappings" not in raw_mapping
            ):
                logger.error(
                    "Invalid mapping format: Expected JSON object with 'tenant_mappings'",
                    bucket=self.bucket_name,
                    key=self.key,
                )
                raise TypeError(
                    f"Invalid mapping format in S3 file {self.key}"
                )
            tenant_mappings = raw_mapping["tenant_mappings"]
            normalized_tenant_mappings = {
                str(key).lower(): str(value)
                for key, value in tenant_mappings.items()
            }

            return normalized_tenant_mappings

        except Exception as e:
            logger.error(
                "Failed to extract tenant mapping details from S3",
                bucket=self.bucket_name,
                key=self.key,
                error=e,
            )
            raise e
