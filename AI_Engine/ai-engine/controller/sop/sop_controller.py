"""
File for defining the sop controller and associated db functions
"""

import typing

import structlog
from sqlalchemy import select, update
from sqlalchemy.dialects import mysql

from controller.interface import ControllerBase
from models_rds.alarm_types import AlarmTypes
from models_rds.raw_alarms import RawAlarms, get_default_partition_key_filter
from models_rds.sop import SOP

log = structlog.get_logger("hakimo.controllers", module="SOP Controller")


class SOPController(ControllerBase):
    def get_sop(
        self,
        tenant_id: typing.Optional[str] = None,
        alarm_type_id: typing.Optional[str] = None,
        door_group_id: typing.Optional[str] = None,
        location_id: typing.Optional[int] = None,
        allow_fallback: bool = True,
    ) -> typing.Optional[SOP]:
        assert (
            tenant_id is not None or location_id is not None
        ), "Either tenant_id or location_id must be provided"
        with self.db.get_session() as sess:
            # If only location_id is provided (without alarm_type_id), look for the default SOP
            if location_id is not None and alarm_type_id is None:
                query = select(SOP).filter(
                    SOP.location_id == location_id, SOP.alarm_type_id.is_(None)
                )
            else:
                query = select(SOP)
                if tenant_id:
                    query = query.filter(SOP.tenant_id == tenant_id)
                if location_id is not None:
                    query = query.filter(SOP.location_id == location_id)
                if alarm_type_id is not None:
                    query = query.filter(SOP.alarm_type_id == alarm_type_id)

            sop = sess.execute(query).scalars().one_or_none()
            if (
                not sop
                and alarm_type_id is not None
                and location_id is not None
                and allow_fallback
            ):
                query = select(SOP).filter(
                    SOP.location_id == location_id, SOP.alarm_type_id.is_(None)
                )
                sop = sess.execute(query).scalars().one_or_none()

            sess.expunge_all()
            return sop

    def _get_sop_alarm_type_ids_by_location_id(
        self, sess, location_id: int
    ) -> typing.List[str]:
        """
        Helper method to get alarm type IDs that have SOPs for a location
        """
        query = select(SOP.alarm_type_id).filter(
            SOP.location_id == location_id, SOP.alarm_type_id.isnot(None)
        )
        return [row[0] for row in sess.execute(query).all()]

    def _get_alarm_types_for_tenant(
        self,
        sess,
        tenant_id: str,
        alarm_type_ids: typing.Optional[typing.List[str]] = None,
        exclude_ids: bool = False,
    ) -> typing.List[AlarmTypes]:
        """
        Helper method to get alarm types for a tenant with optional filtering
        """
        exists_subquery = (
            select(RawAlarms.alarm_type_id)
            .select_from(RawAlarms)
            .filter(
                RawAlarms.alarm_type_id == AlarmTypes.uuid,
                RawAlarms.tenant_id == tenant_id,
                get_default_partition_key_filter(),
            )
        )

        alarm_types_query = select(AlarmTypes).filter(exists_subquery.exists())

        if alarm_type_ids:
            if exclude_ids:
                alarm_types_query = alarm_types_query.filter(
                    AlarmTypes.uuid.notin_(alarm_type_ids)
                )
            else:
                alarm_types_query = alarm_types_query.filter(
                    AlarmTypes.uuid.in_(alarm_type_ids)
                )

        # Print the actual SQL query for performance analysis
        print(alarm_types_query.statement.compile(dialect=mysql.dialect(), compile_kwargs={"literal_binds": True}))

        return sess.execute(alarm_types_query).scalars().all()

    def get_location_sop_alarm_types(
        self,
        location_id: int,
        tenant_id: str,
    ) -> typing.List[typing.Dict[str, typing.Any]]:
        if not location_id or not tenant_id:
            raise ValueError("Both location_id and tenant_id must be provided")

        with self.db.get_session() as sess:
            alarm_type_ids = self._get_sop_alarm_type_ids_by_location_id(
                sess, location_id
            )

            if alarm_type_ids:
                alarm_types = self._get_alarm_types_for_tenant(
                    sess, tenant_id, alarm_type_ids, exclude_ids=False
                )
                result = [
                    {"id": at.uuid, "name": at.alarm_type}
                    for at in alarm_types
                ]
            else:
                result = []

            sess.expunge_all()
            return result

    def add_sop(
        self,
        sop_text: str,
        tenant_id: typing.Optional[str] = None,
        alarm_type_id: typing.Optional[str] = None,
        door_group_id: typing.Optional[str] = None,
        location_id: typing.Optional[int] = None,
    ) -> SOP:
        assert (
            alarm_type_id or location_id
        ), "Either alarm_type_id or location_id must be provided"
        # Check if a SOP with the exact same parameters exists
        existing_sop = self.get_sop(
            tenant_id,
            alarm_type_id,
            door_group_id,
            location_id,
            allow_fallback=False,
        )
        if existing_sop:
            raise Exception("SOP already exists")
        sop = SOP(
            alarm_type_id=alarm_type_id,
            tenant_id=tenant_id,
            sop=sop_text,
            door_group_id=door_group_id,
            location_id=location_id,
        )
        self.db.insert(sop)
        return sop

    def update_sop(
        self,
        sop_id: int,
        sop_text: str,
        alarm_type_id: typing.Optional[str] = None,
        clear_alarm_type: bool = False,
    ) -> None:
        with self.db.get_session() as sess:
            if clear_alarm_type:
                update_stmt = (
                    update(SOP)
                    .where(SOP.int_id == sop_id)
                    .values(sop=sop_text, alarm_type_id=None)
                )
            elif alarm_type_id is not None:
                update_stmt = (
                    update(SOP)
                    .where(SOP.int_id == sop_id)
                    .values(sop=sop_text, alarm_type_id=alarm_type_id)
                )
            else:
                update_stmt = (
                    update(SOP)
                    .where(SOP.int_id == sop_id)
                    .values(sop=sop_text)
                )
            sess.execute(update_stmt)

    def get_by_id(self, sop_id: int) -> SOP:
        with self.db.get_session() as sess:
            select_stmt = select(SOP).where(SOP.int_id == sop_id)
            sop = sess.execute(select_stmt).scalars().one()
            sess.expunge_all()
            return sop

    def get_available_alarm_types(
        self,
        location_id: int,
        tenant_id: str,
    ) -> typing.List[typing.Dict[str, typing.Any]]:
        if not location_id or not tenant_id:
            raise ValueError("Both location_id and tenant_id must be provided")

        with self.db.get_session() as sess:
            existing_alarm_type_ids = (
                self._get_sop_alarm_type_ids_by_location_id(sess, location_id)
            )
            alarm_types = self._get_alarm_types_for_tenant(
                sess, tenant_id, existing_alarm_type_ids, exclude_ids=True
            )
            result = [
                {"id": at.uuid, "name": at.alarm_type} for at in alarm_types
            ]
            sess.expunge_all()
            return result
