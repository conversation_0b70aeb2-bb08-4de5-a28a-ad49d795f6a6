import time
import typing

import structlog
from sqlalchemy import func, select

from common_utils.metrics_definitions import MYSQL_READ_SUMMARY
from controller.interface import ControllerBase
from models_rds.person_profile import PersonProfiles

log = structlog.get_logger(
    "hakimo.controllers", module="Person Profile Controller."
)


class PersonProfileController(ControllerBase):
    def get_person_profiles(
        self,
        tenant_ids: typing.Optional[typing.Sequence[str]] = None,
        location_ids: typing.Optional[typing.Sequence[int]] = None,
        person_profile_id: typing.Optional[typing.Sequence[str]] = None,
        is_enabled: typing.Optional[bool] = None,
        offset: int = 0,
        limit: typing.Optional[int] = None,
    ) -> typing.Tuple[typing.List[PersonProfiles], int]:
        with self.db.get_session() as sess:
            query = select(PersonProfiles)
            if tenant_ids is not None:
                query = query.filter(PersonProfiles.tenant_id.in_(tenant_ids))
            if offset is not None:
                query = query.offset(offset)
            if limit is not None:
                query = query.limit(limit)
            if person_profile_id is not None:
                query = query.filter(PersonProfiles.id == person_profile_id)
            if is_enabled is not None:
                query = query.filter(PersonProfiles.is_enabled == is_enabled)

            person_profiles = sess.execute(query).unique().scalars().all()

            count_query = select(func.count(PersonProfiles.id))
            if tenant_ids is not None:
                count_query = count_query.filter(
                    PersonProfiles.tenant_id.in_(tenant_ids)
                )
            if person_profile_id is not None:
                count_query = count_query.filter(
                    PersonProfiles.id == person_profile_id
                )
            count = sess.execute(count_query).scalar_one()
            sess.expunge_all()
            return person_profiles, count

    def upsert_person_profiles(
        self, person_profiles: typing.Sequence[PersonProfiles]
    ) -> None:
        with self.db.get_session() as sess:
            for person_profile in person_profiles:
                sess.merge(person_profile)

    def get_person_profile_by_id(
        self,
        person_profile_id: str,
        tenant_id: typing.Optional[str] = None,
        location_id: typing.Optional[int] = None,
    ) -> typing.Optional[PersonProfiles]:
        with self.db.get_session() as sess:
            query = select(PersonProfiles).filter(
                PersonProfiles.id == person_profile_id
            )
            if tenant_id is not None:
                query = query.filter(PersonProfiles.tenant_id == tenant_id)
            if location_id is not None:
                query = query.filter(PersonProfiles.location_id == location_id)
            start = time.time()
            person_profile = sess.execute(query).scalars().first()
            sess.expunge_all()
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_READ_SUMMARY.labels(
                tenant=tenant_id,
                query="get_person_profile_by_id",
                table_name="person_profile",
            ).observe(time_taken_ms)
            return person_profile
