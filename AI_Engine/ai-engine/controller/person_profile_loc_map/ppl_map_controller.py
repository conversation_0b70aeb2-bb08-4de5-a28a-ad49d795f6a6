import typing

import structlog
from sqlalchemy import delete, func, select

from controller.interface import ControllerBase
from models_rds.locations import Locations
from models_rds.person_profile import PersonProfiles
from models_rds.person_profile_location_map import PersonProfileLocationMap

log = structlog.get_logger(
    "hakimo", module="PersonProfilesLocationsMapController"
)


class PersonProfilesLocationsMapController(ControllerBase):
    def get_person_profiles_for_location(
        self,
        tenant_ids: typing.Optional[typing.Sequence[str]] = None,
        location_ids: typing.Optional[typing.Sequence[int]] = None,
        person_profile_id: typing.Optional[typing.Sequence[str]] = None,
        is_enabled: typing.Optional[bool] = None,
        offset: int = 0,
        limit: typing.Optional[int] = None,
    ) -> typing.Tuple[typing.List[PersonProfiles], int]:
        with self.db.get_session() as sess:
            query = select(PersonProfiles).join(PersonProfileLocationMap)
            if offset is not None:
                query = query.offset(offset)
            if limit is not None:
                query = query.limit(limit)
            if person_profile_id is not None:
                query = query.filter(PersonProfiles.id.in_(person_profile_id))
            if tenant_ids is not None:
                query = query.filter(PersonProfiles.tenant_id.in_(tenant_ids))
            if location_ids is not None:
                query = query.filter(
                    PersonProfileLocationMap.location_id.in_(location_ids)
                )
            if is_enabled is not None and is_enabled:
                query = query.filter(PersonProfiles.is_enabled == 1)
                query = query.filter(PersonProfileLocationMap.is_active == 1)

            person_profiles = sess.execute(query).unique().scalars().all()

            count_query = select(func.count(PersonProfiles.id)).join(
                PersonProfileLocationMap
            )
            if person_profile_id is not None:
                count_query = count_query.filter(
                    PersonProfiles.id.in_(person_profile_id)
                )
            if tenant_ids is not None:
                count_query = count_query.filter(
                    PersonProfiles.tenant_id.in_(tenant_ids)
                )
            if location_ids is not None:
                query = query.filter(
                    PersonProfileLocationMap.location_id.in_(location_ids)
                )
            if is_enabled is not None and is_enabled:
                query = query.filter(PersonProfiles.is_enabled == 1)
                query = query.filter(PersonProfileLocationMap.is_active == 1)
            count = sess.execute(count_query).scalar_one()
            sess.expunge_all()
            return person_profiles, count

    def get_locations_for_person_profile(
        self, person_profile_id: str, get_all: bool = False
    ) -> typing.List[Locations]:
        query = (
            select(Locations)
            .join(PersonProfileLocationMap)
            .filter(
                PersonProfileLocationMap.person_profile_id == person_profile_id
            )
        )
        if not get_all:
            query = query.filter(PersonProfileLocationMap.is_active == 1)
        with self.db.get_session() as sess:
            locs = sess.execute(query).scalars().all()
            sess.expunge_all()
            return locs

    def add_person_profiles_location_mapping(
        self, person_profile_id: str, location_id: int, is_active: bool = True
    ) -> None:
        mapping = PersonProfileLocationMap(
            person_profile_id=person_profile_id,
            location_id=location_id,
            is_active=is_active,
        )
        with self.db.get_session() as sess:
            sess.add(mapping)

    def delete_person_profiles_location_mapping(
        self, person_profile_id: str, location_id: int
    ) -> None:
        query = (
            delete(PersonProfileLocationMap)
            .where(
                PersonProfileLocationMap.person_profile_id == person_profile_id
            )
            .where(PersonProfileLocationMap.location_id == location_id)
        )
        with self.db.get_session() as sess:
            sess.execute(query)
