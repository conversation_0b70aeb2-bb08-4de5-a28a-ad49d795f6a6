import json
from typing import Dict, Optional

import structlog
from cachetools import T<PERSON><PERSON><PERSON>, cachedmethod

from db_controller.interface import ControllerBase

log = structlog.get_logger("hakimo", module="TenantController")


class TenantController(ControllerBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Create a TTL cache that expires after 60 minutes (3600 seconds)
        self._config_cache = TTLCache(maxsize=128, ttl=3600)

    @cachedmethod(lambda self: self._config_cache)
    def get_tenant_alarm_processing_config_by_id(
        self,
        tenant_id: str,
    ) -> Optional[Dict]:
        """
        Get tenant alarm processing config by tenant_id
        Args:
            tenant_id: The unique identifier of the tenant
        Returns:
            Dictionary containing the alarmProcessingConfig if found, None otherwise
        """
        try:
            db = self.read_db or self.db
            with db.get_session() as sess:
                query = """
                    SELECT t.alarmProcessingConfig
                    FROM tenants t
                    WHERE t.id = :tenant_id
                    """
                result = sess.execute(query, {"tenant_id": tenant_id})
                row = result.fetchone()
                if not row:
                    return None
                alarmProcessingConfig = json.loads(row[0])
                sess.expunge_all()
                return alarmProcessingConfig

        except Exception as e:
            log.error(
                "error_getting_tenant_alarm_processing_config",
                error=e,
                tenant_id=tenant_id,
                exc_info=True,
            )
            raise
