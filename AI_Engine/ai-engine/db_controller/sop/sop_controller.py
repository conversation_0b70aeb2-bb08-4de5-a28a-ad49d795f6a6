from typing import Optional

import structlog
from cachetools import TTL<PERSON>ache, cachedmethod
from sqlalchemy import select

from db_controller.interface import ControllerBase
from models_rds.sop import SOP

log = structlog.get_logger("hakimo", module="SOPController")


class SOPController(ControllerBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Create a TTL cache that expires after 60 minutes (3600 seconds)
        self._sop_cache = TTLCache(maxsize=128, ttl=3600)

    @cachedmethod(lambda self: self._sop_cache)
    def get_sop(
        self,
        location_id: int,
    ) -> Optional[SOP]:
        """
        Get sop by location_id
        Args:
            location_id: ID of the location to retrieve sop for
        Returns:
            SOP if found, None otherwise
        """
        try:
            db = self.read_db or self.db
            with db.get_session() as sess:
                query = select(SOP).filter(SOP.location_id == location_id)
                sop = sess.execute(query).scalars().one_or_none()
                sess.expunge_all()
                return sop
        except Exception as e:
            log.error(
                "error_getting_sop",
                error=str(e),
                location_id=location_id,
                exc_info=True,
            )
            raise
