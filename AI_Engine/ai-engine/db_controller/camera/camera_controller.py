import structlog
from cachetools import TTL<PERSON>ache, cachedmethod

from db_controller.interface import ControllerBase

log = structlog.get_logger("hakimo", module="CameraController")


class CameraController(ControllerBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Create a TTL cache that expires after 60 minutes (3600 seconds)
        self._camera_cache = TTLCache(maxsize=512, ttl=3600)

    def get_camera_by_id_without_cache(self, camera_id: str):
        """
        Get camera by its ID
        Args:
            camera_id: The unique identifier of the camera
        Returns:
            Camera if found, None otherwise
        """
        try:
            db = self.read_db or self.db
            with db.get_session() as sess:
                query = """
                    SELECT
                        c.*
                    FROM cameras c
                    WHERE c.id = :camera_id
                    """
                result = sess.execute(query, {"camera_id": camera_id})
                camera = result.fetchone()
                sess.expunge_all()
                return camera

        except Exception as e:
            log.error(
                "error_getting_camera",
                error=e,
                camera_id=camera_id,
                exc_info=True,
            )
            raise

    @cachedmethod(lambda self: self._camera_cache)
    def get_camera_by_id(
        self,
        camera_id: str,
        include_location: bool = False,
    ):
        """
        Get camera by its ID
        Args:
            camera_id: The unique identifier of the camera
            include_location: Whether to include location data in the query
        Returns:
            Camera if found, None otherwise
        """
        try:
            db = self.read_db or self.db
            with db.get_session() as sess:
                # Commented due to dependency issues while importing Cameras
                # query = select(Cameras)
                # if include_location:
                #     query = query.options(joinedload(Cameras.location))
                # query = query.filter(Cameras.uuid == cam_id)
                # cam = sess.execute(query).scalars().one_or_none()
                # sess.expunge_all()
                # return cam
                query = (
                    """
                    SELECT
                        c.*,
                        l.timezone as location_timezone,
                        l.name as location_name
                    FROM cameras c
                    LEFT JOIN locations l ON c.location_id = l.id
                    WHERE c.id = :camera_id
                    """
                    if include_location
                    else """
                    SELECT * FROM cameras WHERE id = :camera_id
                    """
                )
                result = sess.execute(query, {"camera_id": camera_id})
                camera = result.fetchone()
                sess.expunge_all()

                return camera

        except Exception as e:
            log.error(
                "error_getting_camera",
                error=e,
                camera_id=camera_id,
                exc_info=True,
            )
            raise
