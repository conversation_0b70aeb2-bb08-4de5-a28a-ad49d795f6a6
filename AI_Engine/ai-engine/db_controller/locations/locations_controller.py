from typing import Optional

import structlog
from cachetools import TTL<PERSON>ache, cachedmethod
from sqlalchemy import select

from db_controller.interface import ControllerBase
from models_rds.locations import Locations

log = structlog.get_logger("hakimo", module="LocationsController")


class LocationsController(ControllerBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Create a TTL cache that expires after 60 minutes (3600 seconds)
        self._location_cache = TTLCache(maxsize=128, ttl=3600)

    @cachedmethod(lambda self: self._location_cache)
    def get_location_by_id(
        self,
        location_id: int,
    ) -> Optional[Locations]:
        """
        Get location by its ID
        Args:
            location_id: The unique identifier of the location
        Returns:
            Location if found, None otherwise
        """
        try:
            db = self.read_db or self.db
            with db.get_session() as sess:
                query = select(Locations).filter(Locations.id == location_id)
                loc = sess.execute(query).scalars().one_or_none()
                sess.expunge_all()
                return loc
        except Exception as e:
            log.error(
                "error_getting_location",
                error=e,
                location_id=location_id,
                exc_info=True,
            )
            raise
