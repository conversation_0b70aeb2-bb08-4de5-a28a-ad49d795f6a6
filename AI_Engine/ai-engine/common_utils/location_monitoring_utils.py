import controller
from common_utils.db_pool import db_adapter_pool
from common_utils.redis.redis_adaptor_initializer import redis_adapter_obj
from interfaces.location_monitoring_config import LocationMonitoringConfig


def get_location_monitoring_config(location_id: int, log):
    """
    Fetch location monitoring configuration from Redis if available,
    otherwise fetch from the database and cache it in Redis.
    """
    if location_id is None:
        log.warning(
            "Location ID is None. Cannot fetch monitoring configuration."
        )
        return None

    redis_key = f"location_monitoring_config:{location_id}"

    try:
        # Attempt to fetch configuration from Redis
        location_monitoring_config_json = redis_adapter_obj.get(redis_key)
        if location_monitoring_config_json[1]:
            log.info(
                f"Location monitoring configuration found in Redis for location_id: {location_id} {location_monitoring_config_json}"
            )
            return LocationMonitoringConfig.from_json(
                location_monitoring_config_json[0]
            )
    except Exception as e:
        log.error(
            f"Error fetching location monitoring configuration from Redis for location_id: {location_id}. Error: {e}"
        )

    try:
        # Fetch configuration from the database
        location_details = controller.ControllerMap(
            db_adapter_pool
        ).locations.get_location_by_id(location_id)
        if location_details and location_details.monitoring_config is not None:
            location_monitoring_config = LocationMonitoringConfig.from_json(
                location_details.monitoring_config
            )
            try:
                # Cache the configuration in Redis
                redis_adapter_obj.set(
                    redis_key, location_details.monitoring_config
                )
                log.info(
                    f"Location monitoring configuration cached in Redis for location_id: {location_id}"
                )
            except Exception as e:
                log.error(
                    f"Error caching location monitoring configuration in Redis for location_id: {location_id}. Error: {e}"
                )
            return location_monitoring_config
        else:
            log.warning(
                f"No monitoring configuration found in the database for location_id: {location_id}"
            )
    except Exception as e:
        log.error(
            f"Error fetching location monitoring configuration from the database for location_id: {location_id}. Error: {e}"
        )

    return None
