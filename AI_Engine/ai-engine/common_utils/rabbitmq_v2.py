import random
import time
import typing

import pika
import structlog

from common_utils.rabbitmq import <PERSON><PERSON><PERSON>roll<PERSON>
from common_utils.rpc_utils import retry
from errors import RetryableError

log = structlog.get_logger("hakimo.common_utils", module="RabbitMQV2")


class RabbitControllerV2(RabbitController):
    def __init__(
        self, rabbit_svc: str, port: int, username: str, password: str
    ):
        self._connection_params = pika.ConnectionParameters(
            host=rabbit_svc,
            connection_attempts=3,
            port=port,
            credentials=pika.credentials.PlainCredentials(username, password),
            # Once every 2 minutes
            heartbeat=120,
            blocked_connection_timeout=30,
        )
        self._connection = None
        self._channel = None

    def _heartbeat(self):
        pass

    def _get_connection(self) -> pika.BlockingConnection:
        try:
            if self._connection and self._connection.is_open:
                self._connection.close()
            self._connection = pika.BlockingConnection(
                parameters=self._connection_params
            )
        except pika.exceptions.AMQPConnectionError as e:
            log.error(
                "RabbitMQ AMQPConnectionError v2",
                exception=str(e),
                connection_params=self._connection_params,
            )
            raise RetryableError("RabbitMQ AMQPConnectionError v2")

    def _close(self):
        """Close the channel and connection."""
        try:
            if self._channel and self._channel.is_open:
                self._channel.close()
            if self._connection and self._connection.is_open:
                self._connection.close()
        except Exception as e:
            log.warning(f"Error while closing connection v2: {e}")
        finally:
            self._channel = None
            self._connection = None

    def _create_channel(self):
        """Create a new channel."""
        try:
            self._channel = self._connection.channel()
            self._channel.confirm_delivery()
        except pika.exceptions.AMQPChannelError as e:
            log.error(
                "RabbitMQ channel error v2",
                exception=str(e),
                connection=self._connection,
            )
            raise RetryableError("RabbitMQ channel error v2")
        except pika.exceptions.StreamLostError as e:
            log.error(
                "RabbitMQ stream lost error v2",
                exception=str(e),
                connection=self._connection,
            )
            raise RetryableError("RabbitMQ stream lost error v2")
        except pika.exceptions.ConnectionClosedByBroker as e:
            log.error(
                "RabbitMQ connection closed by broker v2",
                exception=str(e),
                connection=self._connection,
            )
            raise RetryableError("RabbitMQ connection closed by broker v2")
        except pika.exceptions.ConnectionClosed as e:
            log.error(
                "RabbitMQ connection closed v2",
                exception=str(e),
                connection=self._connection,
            )
            raise RetryableError("RabbitMQ connection closed v2")
        except pika.exceptions.AMQPConnectionError as e:
            log.error(
                "RabbitMQ AMQP connection error v2",
                exception=str(e),
                connection=self._connection,
            )
            raise RetryableError("RabbitMQ AMQP connection error v2")
        except Exception as e:
            log.error(
                "RabbitMQ unexpected error v2",
                exception=str(e),
                connection=self._connection,
            )
            raise e

    def _get_channel(self) -> pika.channel.Channel:
        if self._channel and self._channel.is_open:
            return self._channel
        elif self._connection and self._connection.is_open:
            return self._create_channel()
        else:
            self._get_connection()
            self._create_channel()
            return self._channel

    def channel(self) -> pika.channel.Channel:
        return self._get_channel()

    @retry(max_duration_seconds=10, retry_delay=2)
    def get_count(self, queues: typing.Sequence[str]):
        count = 0
        for queue in queues:
            try:
                ch = self.channel()
                q = ch.queue_declare(queue, passive=True)
            except pika.exceptions.ChannelClosedByBroker as exc:
                if exc.reply_code == 404:
                    # Queue hasn't been declared
                    continue
                log.error(
                    "RabbitMQ ChannelClosedByBroker error v2",
                    exception=str(exc),
                    connection=self._connection,
                )
                raise RetryableError("RabbitMQ ChannelClosedByBroker error v2")
            except pika.exceptions.StreamLostError as e:
                log.error(
                    "RabbitMQ StreamLostError in get_count v2",
                    exception=str(e),
                )
                self._connection = None
                self._channel = None
                raise RetryableError(
                    "RabbitMQ StreamLostError in get_count v2"
                )
            except RetryableError as e:
                log.error(
                    "RabbitMQ RetryableError in get_count v2",
                    exception=str(e),
                    connection=self._connection,
                )
                raise e
            except Exception as e:
                log.error(
                    "RabbitMQ unexpected error in get_count v2",
                    exception=str(e),
                    connection=self._connection,
                )
                raise e
            count += q.method.message_count
        return count

    @retry(max_duration_seconds=10, retry_delay=2)
    def purge_all(self, queues: typing.Sequence[str]):
        for queue in queues:
            try:
                ch = self.channel()
                ch.queue_purge(queue)
            except pika.exceptions.ChannelClosedByBroker as exc:
                if exc.reply_code == 404:
                    # Queue hasn't been declared
                    continue
                log.error(
                    "RabbitMQ ChannelClosedByBroker error v2",
                    exception=str(exc),
                    connection=self._connection,
                )
                raise RetryableError("RabbitMQ ChannelClosedByBroker error v2")
            except RetryableError as e:
                log.error(
                    "RabbitMQ RetryableError in purge_all v2",
                    exception=str(e),
                    connection=self._connection,
                )
                raise e
            except Exception as e:
                log.error(
                    "RabbitMQ unexpected error in purge_all v2",
                    exception=str(e),
                    connection=self._connection,
                )
                raise e

    @retry(max_duration_seconds=10, retry_delay=2)
    def get_pending(
        self, queues: typing.Sequence[str], limit: int = 10, ack: bool = False
    ) -> typing.Sequence[str]:
        # shuffle needed to pick from all queues uniformly
        queue_list = list(queues)
        random.shuffle(queue_list)
        body_list: typing.List[str] = []
        for queue in queue_list:
            try:
                ch = self.channel()
                method_frames = []
                while len(body_list) < limit:
                    method_frame, _, body = ch.basic_get(queue)
                    if method_frame:
                        method_frames.append(method_frame)
                    if body is None:
                        log.warning(
                            "No message left from RabbitMQ Queue to consume v2",
                            tenant_id=queue,
                        )
                        break
                    body_list.append(body.decode())
                for method_frame in method_frames:
                    if ack:
                        ch.basic_ack(method_frame.delivery_tag)
                    else:
                        ch.basic_reject(method_frame.delivery_tag)
            except pika.exceptions.ChannelClosedByBroker as exc:
                log.warning(
                    "ChannelClosedByBroker exception while quering tenant RabbitMQ v2",
                    tenant_id=queue,
                    reply_code=exc.reply_code,
                )
                if exc.reply_code == 404:
                    # Queue hasn't been declared
                    continue
                log.error(
                    "RabbitMQ ChannelClosedByBroker error v2",
                    exception=str(exc),
                    connection=self._connection,
                )
                raise RetryableError("RabbitMQ ChannelClosedByBroker error v2")
            except RetryableError as e:
                log.error(
                    "RabbitMQ RetryableError in get_pending v2",
                    exception=str(e),
                    connection=self._connection,
                )
                raise e
            except Exception as e:
                log.error(
                    "RabbitMQ unexpected error in get_pending v2",
                    exception=str(e),
                    connection=self._connection,
                )
                raise e

        return body_list

    def publish(
        self,
        queue_name: str,
        payload: str,
        priority: int = 1,
        max_retries: int = 5,
    ) -> bool:
        # Clip to 0 to 10
        priority = max(0, min(10, priority))
        retries = 0

        while retries <= max_retries:
            try:
                ch = self.channel()
                ch.queue_declare(
                    queue_name,
                    arguments={"x-max-priority": 10},
                    durable=True,
                    exclusive=False,
                    auto_delete=False,
                )
                log.info(
                    "Publishing to RabbitMQ queue v2",
                    queue=queue_name,
                    priority=priority,
                    loc_alarm_id=payload,
                )
                ch.basic_publish(
                    exchange="",
                    routing_key=queue_name,
                    body=payload,
                    properties=pika.BasicProperties(priority=int(priority)),
                )
                log.info(
                    "Payload is published to RabbitMQ queue v2",
                    queue=queue_name,
                    priority=priority,
                    loc_alarm_id=payload,
                )
                return True
            except RetryableError as e:
                log.warning(
                    "RabbitMQ RetryableError in publish v2",
                    queue=queue_name,
                    priority=priority,
                    loc_alarm_id=payload,
                    retry_count=retries,
                    error=str(e),
                )
                time.sleep(1)
                retries += 1
            except Exception as e:
                log.warning(
                    "Failed to publish payload to RabbitMQ v2",
                    queue=queue_name,
                    priority=priority,
                    loc_alarm_id=payload,
                    retry_count=retries,
                    error=str(e),
                )
                time.sleep(1)
                retries += 1

        log.warning(
            "Failed to publish payload to RabbitMQ after retries v2",
            queue=queue_name,
            priority=priority,
            loc_alarm_id=payload,
        )
        return False
