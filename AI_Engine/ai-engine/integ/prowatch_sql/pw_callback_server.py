import json
from http import HTTPStatus

import structlog
from flask import Blueprint, request

log = structlog.get_logger("hakimo", module="prowatch_sql_event_server")


def prowatch_event_blueprint(proc):
    api = Blueprint("prowatch_event_blueprint", __name__)

    @api.route("/event", methods=["POST"])
    def new_appliance_event():
        api_resp = {
            "status": HTTPStatus.OK.value,
            "message": "Processed Successfully",
        }
        try:
            evt_json = request.get_json()
            # proc.single_event_(evt_data=json)
            log.warn(
                "Events via Prowatch Windows Service is not supported, ignoring the event",
                evt=evt_json,
            )
            return api_resp, HTTPStatus.OK.value
        except Exception as e:  # pylint: disable=broad-except
            log.exception(
                "Error in sending genetec event to backend", exec_info=e
            )
            api_resp = {
                "status": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "message": str(e),
            }
            return (
                api_resp,
                HTTPStatus.INTERNAL_SERVER_ERROR.value,
            )

    @api.route("/alarm/ack", methods=["POST"])
    def ack_update_acs_alarm():
        api_resp = {
            "status": HTTPStatus.OK.value,
            "message": "Processed Successfully",
        }
        try:
            r = request.get_json()
            alarm_json = json.loads(r)
            log.debug(
                "Alarm ack received from prowatch",
                data=r,
                alarm_json=alarm_json,
            )
            proc.alarm_ack_(alarm_json=alarm_json)
            return api_resp, HTTPStatus.OK.value
        except Exception as e:  # pylint: disable=broad-except
            log.exception(
                "Error in sending alarm ack event to backend", exec_info=e
            )
            api_resp = {
                "status": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "message": str(e),
            }
            return (
                api_resp,
                HTTPStatus.INTERNAL_SERVER_ERROR.value,
            )

    return api
