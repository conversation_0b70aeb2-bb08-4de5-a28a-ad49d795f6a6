﻿using System;
using System.IO;
using System.Net.Http;
using Newtonsoft.Json;
using Polly;
using Polly.Retry;

namespace TestClientEventService
{
    static public class Utils
    {
        public static string VIDEO_EXPORT_OK = "ok";
        public static string VIDEO_EXPORT_CANCELLED = "Export Cancelled";
        public static string VIDEO_EXPORT_NO_VIDEO = "There was not video available for the specified time range.";
        public static string VIDEO_EXPORT_SERVER_UNAVAILABLE = "The archiver is currently unavailable";
        public static string VIDEO_EXPORT_CONNECTION_LOST = "Lost connection to archiver during export";
        public static string VIDEO_EXPORT_ENTITY_NOT_FOUND = "Entity Guid not found";
        public static string VIDEO_EXPORT_INSUFFICIENT_PRIVILEGES = "The user don't have the privilege from exporting the specified video(s)";
        public static string VIDEO_EXPORT_ENCRYPTED_SEQUENCES = "Export failed: All sequences are encrypted.";
        public static string VIDEO_EXPORT_ERROR = "Video Export Error";

        public static string ToJsonStr(object a)
        {
            return JsonConvert.SerializeObject(a, 
                new JsonSerializerSettings { Error = JSerializer_Error });
        }

        private static void JSerializer_Error(object sender, Newtonsoft.Json.Serialization.ErrorEventArgs e)
        { 
            e.ErrorContext.Handled = true;
        }

        public static string ConstructMediaOutputDirectory(string stagingFolder)
        {
            DateTime utcnow = DateTime.UtcNow;
            string dateDir = utcnow.Year.ToString() + "-" + utcnow.Month.ToString()
                + "-" + utcnow.Day.ToString();
            string hourDir = utcnow.Hour.ToString();
            string subDir = Path.Combine(dateDir, hourDir);
            string finalDir = Path.Combine(stagingFolder, subDir);
            if (!Directory.Exists(finalDir))
            {
                Directory.CreateDirectory(finalDir);
            }
            return finalDir;
        }

        public static AsyncRetryPolicy GetRetryPolicy(int retryCount)
        {
            return Policy.Handle<Exception>().WaitAndRetryAsync(retryCount,
                (retryAttempt) => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
        }
    }
}
