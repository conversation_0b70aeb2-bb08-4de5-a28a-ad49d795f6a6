﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;
using Serilog.Formatting.Json;
using Serilog;
using System.IO;

namespace TestClientEventService
{
    class Program
    {

        private static void ConfigureLogging(string logDir)
        {
            DirectoryInfo dirInfo = Directory.CreateDirectory(logDir);
            string debugFilePath = Path.Combine(logDir, "logs.log");
            string errorFilePath = Path.Combine(logDir, "error.log");
            Log.Logger = new LoggerConfiguration()
                .Enrich.WithProperty("module", "genetec-win-sdk")
                .Enrich.FromLogContext()
                .MinimumLevel.Debug()
                .WriteTo.Console()
                .WriteTo.File(new JsonFormatter(), debugFilePath,
                            restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Debug,
                            rollingInterval: RollingInterval.Day)
                .WriteTo.File(new JsonFormatter(), errorFilePath,
                            restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Error,
                            rollingInterval: RollingInterval.Day)
                .CreateLogger();
        }

        static void Main(string[] args)
        {
            System.Net.ServicePointManager.ServerCertificateValidationCallback = ((sender, certificate, chain, sslPolicyErrors) => true);

            ConfigureLogging("C:\\hakimo\\logs\\");

            Console.WriteLine(" Starting TestClient for Pro-Watch API Event Service ..");
            PwEventClient client = new PwEventClient();
            //client.Start();
           
            Console.ReadLine();

        }
    }
}
