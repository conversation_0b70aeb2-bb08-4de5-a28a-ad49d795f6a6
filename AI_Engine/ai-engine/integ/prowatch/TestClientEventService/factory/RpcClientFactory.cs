﻿
using System.Configuration;
using Serilog;

namespace TestClientEventService
{
    class RpcClientFactory
    {
        private RpcClient rpcClient;

        public RpcClientFactory()
        {
            rpcClient = GetHipRpcInstance();
        }

        public RpcClient GetRpcClient()
        {
            return rpcClient;
        }

        private RpcClient GetHipRpcInstance()
        {
            string hakimoBaseUrl = ConfigurationManager.AppSettings["hipUrl"];
            Log.Debug("Getting RPC client to Hakimo HIP: {hakimoBaseUrl}", hakimoBaseUrl);
            return new RpcClient(hakimoBaseUrl);
        }
    }
}
