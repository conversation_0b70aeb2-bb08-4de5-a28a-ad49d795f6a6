﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Configuration;
using Microsoft.AspNet.SignalR;
using Microsoft.AspNet.SignalR.Client;

using HoneywellAccess.ProWatch.PWDataModel.DataContracts;
using HoneywellAccess.ProWatch.PWDataModel.StatusContracts;
using HoneywellAccess.ProWatch.PWLogger;
using HoneywellAccess.ProWatch.PWCommon;
using Serilog;
using Microsoft.AspNet.SignalR.Client.Http;
using System.Runtime.CompilerServices;

namespace TestClientEventService
{
    class PwEventClient
    {
        private HubConnection Conn;
        private IHubProxy EventSrvProxy;
        private string url = "";
        private string username = "";
        private string workstation = "";
        public static IPWLog PwLog = null;
        private static int RETRIES = 5;

        private Timer myT;
        static ulong evCnt = 0, alCnt = 0;
        static ulong cnt = 1000, incr = 1000;
        static ulong secswithsamecnt = 0;
        static ulong lastcnt = 0;
        static bool samecnt = false;


        static ulong timessamecnt = 0;
        
        private static RpcClient RPC = new RpcClientFactory().GetRpcClient();

        public PwEventClient()
        {
            Conn = null;
            EventSrvProxy = null;
            
            PwLog = new PWLog();

            url = ConfigurationManager.AppSettings["url"];
            username = ConfigurationManager.AppSettings["username"];
            workstation = ConfigurationManager.AppSettings["workstation"];

            myT = new Timer(new TimerCallback(Timer_OnFiveMinutePulse));
            myT.Change(1000, 0);

            //String evt = "4/3/2025 3:57:30 PM,0x00716E8EBC0CF46C4D2C87ADB36B768ED9D5,4/3/2025 3:57:30 PM,4/3/2025 3:57:30 PM,Terminated Card Attempt,410,AWS01-S101::05010005000800,AWS01 - Test Reader,,,,";
            //Dictionary<string, object> dictionary = new Dictionary<string, object>();
            //dictionary.Add("Prop1", "hello world!");
            //dictionary.Add("Prop2", 3893);
            //ShipEvent("test_event", dictionary);
            //return;

            if ( !string.IsNullOrEmpty(url) )
            {
                Conn = new HubConnection( url );

                if( Conn != null )
                {
                    //DEBUG ERRORS
                    //Conn.TraceLevel = TraceLevels.All;
                    //Conn.TraceWriter = Console.Out;

                    EventSrvProxy = Conn.CreateHubProxy("PWEventService");

                    if (EventSrvProxy != null)
                    {
                        if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(workstation))
                        {
                            EventSrvProxy["userName"] = username;
                            EventSrvProxy["wrkstName"] = workstation;
                        }

                        //register handlers
                        //EventSrvProxy.On<string>("onBroadcastMessage", message => Console.WriteLine(DateTime.Now.ToString() + " : " + message));
                        EventSrvProxy.On<string>("onBroadcastMessage", OnBroadcastMessage);
                        EventSrvProxy.On<PwEvent>("onProwatchEvent", OnProwatchEvent);
                        EventSrvProxy.On<PwEvent>("onProwatchAlarm", OnProwatchAlarm);
                        EventSrvProxy.On<PwEventDisposition>("onProwatchAlarmDisposition", OnProwatchAlarmDisposition);
                    }

                }
            }
        }       

        public void Start()
        {
            Console.WriteLine(" Starting Event client ..");
            Conn.Start().Wait();

            if (EventSrvProxy != null)
            {
                if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(workstation))
                {
                    EventSrvProxy["userName"] = username;
                    EventSrvProxy["wrkstName"] = workstation;

                    Console.Title = "User: [" + username + "]  Wrkst: [" + workstation + "]";
                }
                PwStatus status = null;
                try
                {
                    status = EventSrvProxy.Invoke<PwStatus>("subscribe").Result;
                    //EventSrvProxy.Invoke("TestMessage", "testdata");
                    string msg = "Subscribe Status :" + Convert.ToString(status.TransNumber) + " - " + Convert.ToString(status.TransStatus) + " - " + status.TransStatusText;

                    Console.WriteLine(DateTime.Now.ToString() + " : " + msg);
                }
                catch (Exception ex)
                {
                    // Handle any other exceptions
                    Console.WriteLine($"An unexpected error occurred: {ex.Message}");
                }
                finally
                {
                    // Code that always executes, regardless of an exception
                    Console.WriteLine("Execution complete.");
                }

                //string evntHeader = Util.EventHeaderToString();
                //PwLog.WriteToLog(evntHeader, PwLogCategory.WebAPIEvents, PwLogSeverity.Info, "Header");

                //string dispHeader = Util.DispHeaderToString();
                //PwLog.WriteToLog(dispHeader, PwLogCategory.WebAPIDisps, PwLogSeverity.Info, "Header");
            }      
        }

        public static void OnBroadcastMessage(string message)
        {
            Console.WriteLine(DateTime.Now.ToString() + " : " + message);
            ShipEvent("broadcast_event", message);
        }
             
        public static void OnProwatchEvent(PwEvent pwEvent)
        {
            string evntmsg = "";
            evntmsg = EventHelper.EventToString(pwEvent);
            Console.WriteLine(DateTime.Now.ToString() + " : " + evntmsg);
            PwLog.WriteToLog(evntmsg, PwLogCategory.WebAPIEvents, PwLogSeverity.Info, "Events");

            Dictionary<string, object> evtDict = (Dictionary<string, object>)ObjectExtensions.AsDictionary(pwEvent);
            ShipEvent("pw_event", evntmsg);

            evCnt++;
        }

        public static void OnProwatchAlarm(PwEvent pwEvent)
        {
            string evntmsg = "";
            evntmsg = EventHelper.EventToString(pwEvent);
            Console.WriteLine(DateTime.Now.ToString() + " : " + evntmsg);
            PwLog.WriteToLog(evntmsg, PwLogCategory.WebAPIAlarms, PwLogSeverity.Info, "ALarms");

            IDictionary<string, object> evtDict = ObjectExtensions.AsDictionary(pwEvent);
            ShipEvent("pw_alarm", evtDict);

            ShipEvent("pw_alarm", evntmsg);

            alCnt++;

        }

        public static void OnProwatchAlarmDisposition(PwEventDisposition pwDispEvent)
        {
            string evntmsg = "";
            evntmsg = EventHelper.DispToString(pwDispEvent);
            Console.WriteLine(DateTime.Now.ToString() + " : " + evntmsg);
            PwLog.WriteToLog(evntmsg, PwLogCategory.WebAPIDisps, PwLogSeverity.Info, "Events");

            IDictionary<string, object> evtDict = ObjectExtensions.AsDictionary(pwDispEvent);
            ShipEvent("disposition_alarm_event", evtDict);
        }

        private static void ShipEvent(String eventType, Dictionary<string, object> evt)
        {
            if(evt == null)
            {
                Log.Warning("No Data to send to HIP, event is null");
                return;
            }

            Log.Information("PW Event Triggered: {event_type}, " + "Event: {event}",
                    eventType, evt);
            string response = "";
            int retry = 0;
            try
            {
                var retryPolicy = Utils.GetRetryPolicy(RETRIES);

                retryPolicy.ExecuteAsync(async () =>
                {
                    Log.Information("Starting sending event to HIP: EventType: {event_type}, "
                        + "Event: {event}, Retry: {retry}",
                        eventType, evt, ++retry);
                    response = await RPC.SendEvent("/alarm/ack", evt);
                });
                Log.Information("Sent event to HIP: EventType: {event_type}, "
                        + "Event: {event}, Retry: {retry}",
                        eventType, evt, retry);
            }
            catch (Exception ex)
            {
                response = ex.Message;
                Log.Error("Error in sending event to HIP: {response}, EventType: {event_type}, "
                        + "Event: {event}", Utils.ToJsonStr(response),
                eventType, evt);
            }
        }

        private static void ShipEvent(String eventType, object evt)
        {
            if (evt == null)
            {
                Log.Warning("No Data to send to HIP, event is null");
                return;
            }

            Log.Information("PW Event Triggered: {event_type}, " + "Event: {event}",
                    eventType, evt);
            string response = "";
            int retry = 0;
            try
            {
                var retryPolicy = Utils.GetRetryPolicy(RETRIES);

                retryPolicy.ExecuteAsync(async () =>
                {
                    Log.Information("Starting sending event to HIP: EventType: {event_type}, "
                        + "Event: {event}, Retry: {retry}",
                        eventType, evt, ++retry);
                    response = await RPC.SendEvent("/alarm/ack", evt);
                });
                Log.Information("Sent event to HIP: EventType: {event_type}, "
                        + "Event: {event}, Retry: {retry}",
                        eventType, evt, retry);
            }
            catch (Exception ex)
            {
                response = ex.Message;
                Log.Error("Error in sending event to HIP: {response}, EventType: {event_type}, "
                        + "Event: {event}", Utils.ToJsonStr(response),
                eventType, evt);
            }
        }

        public static void Timer_OnFiveMinutePulse(object stateInfo)
        {
            Timer myT = (Timer)stateInfo;

            if( lastcnt == 0)
                lastcnt = evCnt;

            if (evCnt / cnt > 0)
            {
                Console.WriteLine("{0} No of Events = [{1}]", DateTime.Now, evCnt);
                ulong q = evCnt / cnt;
                ulong rem = ((evCnt % cnt) /10 ) * 10;

                if(incr > rem)
                    cnt = cnt * ( evCnt /  cnt) + incr;
                else
                    cnt = cnt * (evCnt / cnt) + rem;

                secswithsamecnt = 0;
                lastcnt = evCnt;
                timessamecnt = 0;
            }
            else // last set of events catching up
            {
                if(lastcnt < evCnt)
                {
                    secswithsamecnt = 0;
                    lastcnt = evCnt;
                    timessamecnt = 0;
                }
                else if (lastcnt == evCnt)
                {
                    secswithsamecnt++;

                    if(( secswithsamecnt >= 5) && timessamecnt < 3)
                    {
                        Console.WriteLine("{0} No of Events = [{1}]", DateTime.Now, evCnt);
                        secswithsamecnt = 0;
                        timessamecnt++;
                    }
                }
                
            }

             myT.Change(1000, 0);
        }
    }
}
