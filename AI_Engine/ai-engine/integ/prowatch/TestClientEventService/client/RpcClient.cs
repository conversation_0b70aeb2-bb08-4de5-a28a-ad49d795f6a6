﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using Serilog;
using Polly;
using Polly.Retry;

namespace TestClientEventService
{
    class RpcClient
    {
        private readonly string baseUri;
        private readonly HttpClient httpClient;
        public HttpClientHandler _httpClientHandler = new HttpClientHandler();

        public RpcClient(string baseUri)
        {
            this.baseUri = baseUri;
            httpClient = new HttpClient(_httpClientHandler, false);
        }

        public async Task<string> SendEvent(string path, object payload)
        {
            var stringContent = new StringContent(Utils.ToJsonStr(payload), 
                Encoding.UTF8, "application/json");
            var response = await httpClient.PostAsync(this.baseUri + path, stringContent);
            response.EnsureSuccessStatusCode();
            Log.Information("did rpc: {path}, {data}", path, stringContent);
            if (response.StatusCode != System.Net.HttpStatusCode.OK)
            {
                Log.Error("rpc failure: {path}, {data}", path, stringContent);
                var msg = new
                {
                    Code = (int)response.StatusCode,
                    Status = response.ReasonPhrase
                };
                return msg.ToString();
            }

            string content = await response.Content.ReadAsStringAsync();
            Log.Information("rpc success: {content}", content);
            return content;
        }
    }
}
