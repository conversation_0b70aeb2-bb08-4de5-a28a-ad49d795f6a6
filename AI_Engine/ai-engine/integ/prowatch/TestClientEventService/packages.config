﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Microsoft.AspNet.SignalR.Client" version="2.2.0" targetFramework="net45" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.TimeProvider" version="8.0.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="Polly" version="8.5.2" targetFramework="net48" />
  <package id="Polly.Core" version="8.5.2" targetFramework="net48" />
  <package id="Serilog" version="4.2.0" targetFramework="net48" />
  <package id="Serilog.Sinks.Console" version="6.0.0" targetFramework="net48" />
  <package id="Serilog.Sinks.File" version="6.0.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.ComponentModel.Annotations" version="4.5.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="8.0.1" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Threading.Channels" version="8.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>