import gevent.monkey

gevent.monkey.patch_all()

import prometheus_client as prom
import structlog

from common_utils.signal_handler import setup_debug_signal_handler
from integ.common.acs_event_processor import acs_event_processor_instance

log = structlog.get_logger("hakimo", module="ACS", klass="ACS_Alarm_Runner")


def runner():
    acs = acs_event_processor_instance()
    setup_debug_signal_handler(log)
    acs.process_alarms()


if __name__ == "__main__":
    prom.start_http_server(8800)
    runner()
