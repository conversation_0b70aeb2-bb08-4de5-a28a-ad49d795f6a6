import json
import time
import traceback
from datetime import UTC, datetime
from typing import Any, Dict, List, Optional, Union

import boto3
import structlog

from ..publisher_config import PublisherConfig

logger = structlog.get_logger("hakimo", module="EventSyncSQSPublisher")


class SyncSQSPublisher:
    def __init__(
        self,
        config: PublisherConfig,
    ):
        self.config = config
        self.session = boto3.Session(
            aws_access_key_id=config.aws_access_key_id,
            aws_secret_access_key=config.aws_secret_access_key,
            aws_session_token=config.aws_session_token,
            region_name=config.aws_region,
        )
        self.is_fifo_queue = config.queue_url.endswith(".fifo")
        self.client = None
        self._initialized = False

    def initialize(self) -> None:
        """Initialize the SQS client with detailed diagnostics"""
        try:
            logger.info(
                "Initializing SQS client", region=self.config.aws_region
            )

            # Create client
            self.client = self.session.client(
                "sqs",
                config=boto3.session.Config(
                    connect_timeout=self.config.connection_timeout,
                    read_timeout=self.config.read_timeout,
                ),
            )

            # Verify we can access the queue
            try:
                queue_name = self.config.queue_url.split("/")[-1]
                logger.info("Verifying queue exists", queue_name=queue_name)

                # Attempt to get queue attributes with retries
                retries = 0
                last_exception = None

                while retries <= self.config.max_retries:
                    try:
                        logger.debug(
                            f"Queue attributes request attempt {retries+1}/{self.config.max_retries+1}"
                        )

                        # Get queue attributes to verify access
                        response = self.client.get_queue_attributes(
                            QueueUrl=self.config.queue_url,
                            AttributeNames=["QueueArn"],
                        )

                        if (
                            "Attributes" in response
                            and "QueueArn" in response["Attributes"]
                        ):
                            logger.info(
                                "Successfully connected to SQS queue",
                                queue_url=self.config.queue_url,
                                queue_arn=response["Attributes"]["QueueArn"],
                            )
                            self._initialized = True
                            return
                        else:
                            logger.error(
                                "Failed to verify SQS queue - unexpected response",
                                response=response,
                            )
                            # Continue with retry
                    except Exception as e:
                        last_exception = e
                        logger.warning(
                            f"Queue attributes request attempt {retries+1} failed",
                            error=str(e),
                            error_type=type(e).__name__,
                        )

                    # Retry logic
                    retries += 1
                    if retries <= self.config.max_retries:
                        backoff = min(
                            self.config.max_backoff,
                            self.config.base_backoff * (2 ** (retries - 1)),
                        )
                        logger.info(f"Backing off for {backoff}s before retry")
                        time.sleep(backoff)

                # All retries failed
                logger.error(
                    "Failed to get queue attributes after all retries",
                    error=str(last_exception)
                    if last_exception
                    else "Unknown error",
                    error_type=type(last_exception).__name__
                    if last_exception
                    else "Unknown",
                    retries=retries - 1,
                )
                if last_exception:
                    raise last_exception
                else:
                    raise Exception(
                        "Failed to get queue attributes after all retries"
                    )

            except Exception as e:
                logger.error(
                    "Failed to verify SQS queue access",
                    error=str(e),
                    error_type=type(e).__name__,
                    queue_url=self.config.queue_url,
                )
                raise
        except Exception as e:
            logger.error(
                "Failed to initialize SQS client",
                error=str(e),
                error_type=type(e).__name__,
                region=self.config.aws_region,
            )
            raise

    def _format_message_attributes(
        self, attributes: Dict[str, Any]
    ) -> Dict[str, Dict[str, str]]:
        """Convert simplified message attributes to SQS format"""
        formatted_attributes = {}
        for key, value in attributes.items():
            # Check if already in SQS format
            if (
                isinstance(value, dict)
                and "DataType" in value
                and "StringValue" in value
            ):
                formatted_attributes[key] = value
            else:
                # Convert simple format to SQS format
                formatted_attributes[key] = {
                    "DataType": "String",
                    "StringValue": str(value),
                }
        return formatted_attributes

    def publish_message(
        self,
        message_body: Union[str, Dict[str, Any]],
        message_attributes: Optional[Dict[str, Any]] = None,
        message_group_id: Optional[str] = None,
        deduplication_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Publish message with comprehensive error handling and diagnostics"""

        # Check initialization
        if not self.client:
            logger.info("Client not initialized, initializing now")
            self.initialize()

        # Prepare message
        try:
            body = (
                message_body
                if isinstance(message_body, str)
                else json.dumps(message_body)
            )
            logger.debug("Prepared message body", body_size=len(body))

            # Format message attributes
            formatted_attributes = {}
            if message_attributes:
                formatted_attributes = self._format_message_attributes(
                    message_attributes
                )
                logger.debug(
                    "Formatted message attributes",
                    attributes=formatted_attributes,
                )

            send_params = {
                "QueueUrl": self.config.queue_url,
                "MessageBody": body,
            }

            if formatted_attributes:
                send_params["MessageAttributes"] = formatted_attributes

            # Add FIFO queue parameters if needed
            if self.is_fifo_queue:
                # Always provide a MessageGroupId for FIFO queues - it's required
                send_params["MessageGroupId"] = message_group_id or "default"

                # Handle deduplication
                if deduplication_id:
                    send_params["MessageDeduplicationId"] = deduplication_id
                elif not self.config.content_based_deduplication:
                    # Generate a deduplication ID if content-based deduplication is not enabled
                    timestamp = datetime.now(UTC).timestamp()
                    send_params["MessageDeduplicationId"] = (
                        f"{timestamp}-{id(message_body)}"
                    )

            logger.debug("Sending message to SQS", parameters=send_params)

            # Attempt to send with retries
            retries = 0
            last_exception = None

            while retries <= self.config.max_retries:
                try:
                    logger.debug(
                        f"Publish attempt {retries+1}/{self.config.max_retries+1}"
                    )

                    # Perform the actual send
                    response = self.client.send_message(**send_params)

                    # Verify response
                    if "MessageId" in response:
                        logger.info(
                            "Message published successfully",
                            message_id=response["MessageId"],
                            queue_url=self.config.queue_url,
                            retry=retries,
                        )
                        return response
                    else:
                        logger.warning(
                            "Message publish response missing MessageId",
                            response=response,
                        )
                        # Continue with retry
                except Exception as e:
                    last_exception = e
                    logger.warning(
                        f"Publish attempt {retries+1} failed",
                        error=str(e),
                        error_type=type(e).__name__,
                    )

                # Retry logic
                retries += 1
                if retries <= self.config.max_retries:
                    backoff = min(
                        self.config.max_backoff,
                        self.config.base_backoff * (2 ** (retries - 1)),
                    )
                    logger.info(f"Backing off for {backoff}s before retry")
                    time.sleep(backoff)

            # All retries failed
            logger.error(
                "Failed to publish message after all retries",
                error=str(last_exception)
                if last_exception
                else "Unknown error",
                error_type=type(last_exception).__name__
                if last_exception
                else "Unknown",
                retries=retries - 1,
            )
            if last_exception:
                raise last_exception
            else:
                raise Exception("Failed to publish message after all retries")

        except Exception as e:
            logger.error(
                "Error in publish_message",
                error=str(e),
                error_type=type(e).__name__,
                stack_trace=traceback.format_exc(),
            )
            raise

    def publish_batch(
        self, messages: List[Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Publish a batch of messages to SQS"""
        if not self.client:
            self.initialize()

        if not messages:
            return {"Successful": [], "Failed": []}

        # SQS batch size limit is 10
        batch_size = min(10, len(messages))
        entries = []

        for i, msg in enumerate(messages[:batch_size]):
            body = msg.get("MessageBody")
            if not isinstance(body, str) and body is not None:
                body = json.dumps(body)

            # Format message attributes if provided
            msg_attributes = msg.get("MessageAttributes", {})
            formatted_attributes = self._format_message_attributes(
                msg_attributes
            )

            entry = {
                "Id": str(i),
                "MessageBody": body or json.dumps(msg.get("Body", {})),
                "MessageAttributes": formatted_attributes,
            }

            # Add FIFO-specific attributes if needed
            if self.is_fifo_queue:
                # MessageGroupId is required for FIFO queues
                entry["MessageGroupId"] = msg.get("MessageGroupId", "default")

                # Handle deduplication ID
                if "MessageDeduplicationId" in msg:
                    entry["MessageDeduplicationId"] = msg[
                        "MessageDeduplicationId"
                    ]
                elif not self.config.content_based_deduplication:
                    # Generate a deduplication ID if content-based deduplication is not enabled
                    timestamp = datetime.now(UTC).timestamp()
                    entry["MessageDeduplicationId"] = (
                        f"{timestamp}-{i}-{id(msg)}"
                    )

            entries.append(entry)

        try:
            retries = 0
            while True:
                try:
                    response = self.client.send_message_batch(
                        QueueUrl=self.config.queue_url, Entries=entries
                    )

                    # Handle partial failures
                    failed = response.get("Failed", [])
                    if failed:
                        logger.warning(
                            "Some messages failed to publish",
                            failed_count=len(failed),
                            total=len(entries),
                            failure_reasons=[f["Message"] for f in failed],
                        )

                    logger.info(
                        "Batch published",
                        successful=len(response.get("Successful", [])),
                        failed=len(failed),
                    )

                    return response
                except Exception as e:
                    retries += 1
                    if retries >= self.config.max_retries:
                        raise

                    backoff = min(
                        self.config.max_backoff,
                        self.config.base_backoff * (2 ** (retries - 1)),
                    )
                    logger.warning(
                        "Error publishing batch, retrying",
                        error=str(e),
                        retry=retries,
                        backoff=backoff,
                    )
                    time.sleep(backoff)
        except Exception as e:
            logger.error("Failed to publish batch after retries", error=str(e))
            raise

    def close(self) -> None:
        """Close the SQS client connection"""
        if self.client:
            self.client = None
            logger.info("SQS publisher closed")


class SyncSQSPublisherSingleton:
    _instance = None

    @classmethod
    def get_instance(cls, config: PublisherConfig = None) -> SyncSQSPublisher:
        if cls._instance is None:
            if config is None:
                raise ValueError(
                    "Config must be provided for first initialization"
                )
            cls._instance = SyncSQSPublisher(config)
            cls._instance.initialize()
        return cls._instance
