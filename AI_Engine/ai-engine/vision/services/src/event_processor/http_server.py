import argparse
import os

import redis
import structlog
from flask import Flask, jsonify, request

from config import backend_config as config
from db_controller import DBControllerV1
from db_model_rds.rds_client import RDSClient
from models_rds.alarm_group import AlarmGroup
from vision.services.src.event_processor.handlers.event_types import (
    add_event_redis,
)
from vision.services.src.event_processor.models.events import (
    EventState,
    Resolution,
    Severity,
)

logger = structlog.get_logger("hakimo", module="http_server_event_processor")

app = Flask(__name__)

rds = RDSClient()
db = rds.db_adapter
ctrl_map = DBControllerV1(db)

# Initialize Redis connection
redis_client = redis.Redis(
    host=config.HAIE.redis[
        "visionServiceName"
    ],  # Replace with your Redis host
    port=config.HAIE.redis["port"],  # Replace with your Redis port
    db=0,  # Default database
    decode_responses=True,
)

workers = int(os.environ.get("HTTP_WORKERS", 2))


@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "ok"})


@app.route("/readiness", methods=["GET"])
def readiness_check():
    try:
        # Check database connection
        db.engine.execute("SELECT 1")

        # Check Redis connection
        redis_client.ping()

        return jsonify({"status": "ready"})
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return jsonify({"status": "not ready", "error": str(e)}), 503


@app.route("/liveness", methods=["GET"])
def liveness_check():
    return jsonify({"status": "alive"})


@app.route("/cameras/<camera_id>", methods=["GET"])
def get_camera_info(camera_id):
    """Get camera info for a given camera id"""
    camera_controller = ctrl_map.camera
    camera_info = camera_controller.get_camera_by_id_without_cache(camera_id)
    if camera_info:
        # Convert Row object to dictionary
        camera_dict = dict(camera_info._mapping)
        return jsonify(camera_dict)
    return jsonify({"error": "Camera not found"}), 404


@app.route(
    "/camera_group/<camera_group_id>/tenant/<tenant_id>/severity/<severity>",
    methods=["POST"],
)
def create_alarm_group(tenant_id, camera_group_id, severity):
    """Create an alarm group with the provided data"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ["timestamp_utc"]
        for field in required_fields:
            if field not in data:
                return (
                    jsonify({"error": f"Missing required field: {field}"}),
                    400,
                )

        key = f"alarm_group:{camera_group_id}:{tenant_id}:{severity}"

        # Fetch the alarm_group_id from Redis
        alarm_group_id = redis_client.hget(key, "alarm_group_id")

        logger.info(
            "Received create alarm group request",
            tenant_id=tenant_id,
            severity=severity,
            timestamp_utc=data["timestamp_utc"],
            camera_group_id=camera_group_id,
        )

        if alarm_group_id:
            logger.info(
                "Retrieved alarm group ID",
                tenant_id=tenant_id,
                severity=severity,
                camera_group_id=camera_group_id,
                alarm_group_id=alarm_group_id,
            )
            return jsonify({"status": "ok", "alarm_group_id": alarm_group_id})
        else:
            logger.warning(
                "Alarm group ID not found in Redis",
                tenant_id=tenant_id,
                severity=severity,
                camera_group_id=camera_group_id,
            )
            alarm_group_controller = ctrl_map.alarm_group

            # Convert timestamp from milliseconds to datetime
            from datetime import datetime

            try:
                # Convert the timestamp (in milliseconds) to a datetime object
                timestamp_ms = int(data["timestamp_utc"])
                timestamp_datetime = datetime.fromtimestamp(
                    timestamp_ms / 1000
                )
            except (ValueError, TypeError) as e:
                logger.error(
                    "Failed to convert timestamp",
                    tenant_id=tenant_id,
                    severity=severity,
                    camera_group_id=camera_group_id,
                    timestamp=data["timestamp_utc"],
                    error=str(e),
                )
                return (
                    jsonify(
                        {
                            "error": f"Invalid timestamp format: {data['timestamp_utc']}"
                        }
                    ),
                    400,
                )

            alarm_group = AlarmGroup(
                id=alarm_group_id,
                severity=severity,
                camera_group_id=camera_group_id,
                tenant_id=tenant_id,
                start_time_utc=timestamp_datetime,
                state=EventState.PENDING,
                resolution=Resolution.OPEN,
            )
            try:
                alarm_group_stored_id, created, severity = (
                    alarm_group_controller.create_alarm_group(alarm_group)
                )
                if severity == Severity.HIGH:
                    add_event_redis(
                        alarm_group_stored_id,
                        Severity.HIGH,
                        tenant_id,
                        camera_group_id,
                    )
                else:
                    add_event_redis(
                        alarm_group_stored_id,
                        Severity.LOW,
                        tenant_id,
                        camera_group_id,
                    )
                return (
                    jsonify(
                        {
                            "status": "ok",
                            "alarm_group_id": alarm_group_stored_id,
                        }
                    ),
                    200,
                )
            except Exception as e:
                logger.error(
                    "Failed to create alarm group in database",
                    tenant_id=tenant_id,
                    severity=severity,
                    camera_group_id=camera_group_id,
                    error=str(e),
                )
                return (
                    jsonify(
                        {
                            "error": "Failed to create alarm group in database",
                            "details": str(e),
                        }
                    ),
                    500,
                )
    except redis.RedisError as e:
        logger.error(
            "Redis connection error",
            tenant_id=tenant_id,
            severity=severity,
            camera_group_id=camera_group_id,
            error=str(e),
        )
        return (
            jsonify({"error": "Redis connection error", "details": str(e)}),
            500,
        )
    except Exception as e:
        logger.error(
            "Unexpected error in create_alarm_group",
            tenant_id=tenant_id,
            severity=severity,
            camera_group_id=camera_group_id,
            error=str(e),
        )
        return (
            jsonify({"error": "Internal server error", "details": str(e)}),
            500,
        )


def create_app():
    """Create and configure the Flask application"""
    logger.info("HTTP server application configured")
    return app


def run_with_gunicorn():
    """Entry point for gunicorn to run the HTTP server"""
    return create_app()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Event processor HTTP server")
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to bind the server to (default: 0.0.0.0)",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8080,
        help="Port to bind the server to (default: 8080)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Run the server in debug mode",
    )

    args = parser.parse_args()

    # Start the Flask app
    app.run(host=args.host, port=args.port, debug=args.debug)
