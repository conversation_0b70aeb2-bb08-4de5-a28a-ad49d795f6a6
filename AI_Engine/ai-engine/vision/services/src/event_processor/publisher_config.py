from dataclasses import dataclass, field
from typing import Dict, Optional

from common_utils_v1.io_helpers import read_file
from config import backend_config as config


@dataclass
class PublisherConfig:
    """Configuration for the AsyncSQSPublisher."""

    # SQS queue configuration
    queue_url: str

    # AWS authentication
    aws_region: str
    # AWS Configuration
    aws_access_key_id: Optional[str] = read_file(
        config.HAIE.AWS_ACCESS_KEY_ID, missing=""
    )
    aws_secret_access_key: Optional[str] = read_file(
        config.HAIE.AWS_SECRET_KEY, missing=""
    )
    aws_session_token: Optional[str] = None

    # FIFO queue settings
    content_based_deduplication: bool = False

    # Retry configuration
    max_retries: int = 3
    base_backoff: float = 0.5  # seconds
    max_backoff: float = 30.0  # seconds

    # Default attributes to add to all messages
    default_message_attributes: Dict[str, Dict[str, str]] = field(
        default_factory=dict
    )

    # Batch publishing settings
    max_batch_size: int = 10  # SQS limit is 10

    # CloudWatch metrics settings
    enable_metrics: bool = False
    metrics_namespace: str = "Hakimo/SQSPublisher"

    # Performance settings
    connection_timeout: float = 5.0  # seconds
    read_timeout: float = 30.0  # seconds
