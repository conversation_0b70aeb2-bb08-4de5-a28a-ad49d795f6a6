import structlog

from config import backend_config as config

from ..models.events import DetectionEvent

logger = structlog.get_logger("hakimo", module="SafetyChecker")

# Constants
SAFETY_THRESHOLD = config.HAIE.VISION_SAFETY_THRESHOLD
SAFETY_COUNT_TTL = config.HAIE.VISION_SAFETY_COUNT_TTL


class SafetyChecker:
    def __init__(self, redis_client):
        self.redis_client = redis_client

    def check_all_above_threshold(self, event: DetectionEvent) -> bool:
        """
        Check if all persons in the event have counts above the safety threshold.

        Args:
            event: DetectionEvent containing tenant_id and metadata

        Returns:
            bool: True if all counts are above threshold, False otherwise
        """
        self.initialize_event(event)

        tenant_id = event.tenant_id
        persons = event.metadata.persons

        if not persons:
            return False

        # Create pipeline for batch operations
        pipeline = self.redis_client.pipeline()

        # Queue all get operations
        for person in persons:
            key = f"safety:{tenant_id}:{person.person_id}"
            pipeline.get(key)

        # Execute pipeline
        try:
            results = pipeline.execute()
        except Exception as e:
            logger.error("Pipeline execution failed", error=str(e))
            return False

        # Check results
        for index, count_str in enumerate(results):
            if count_str is None:
                logger.error(
                    "Error fetching count for person",
                    person_id=persons[index].person_id,
                )
                return False

            try:
                count = int(count_str or "0")
                if count <= SAFETY_THRESHOLD:
                    return False
            except ValueError:
                logger.error(
                    "Invalid count value for person",
                    person_id=persons[index].person_id,
                )
                return False

        return True

    def initialize_event(self, event: DetectionEvent) -> None:
        """
        Initialize safety counts for all persons in the event.

        Args:
            event: DetectionEvent containing tenant_id, camera_id, and metadata
        """
        tenant_id = event.tenant_id
        camera_id = event.camera_id
        persons = event.metadata.persons

        if not persons:
            logger.error(
                "No persons found in event",
                tenant_id=tenant_id,
                camera_id=camera_id,
            )
            return

        pipeline = self.redis_client.pipeline()

        for person in persons:
            key = f"safety:{tenant_id}:{person.person_id}"
            # Set initial count to 0 if key doesn't exist
            pipeline.set(key, "0", ex=SAFETY_COUNT_TTL, nx=True)
            # Ensure TTL is set
            pipeline.expire(key, SAFETY_COUNT_TTL)

        try:
            pipeline.execute()
        except Exception as e:
            logger.error("Failed to initialize event", error=str(e))
