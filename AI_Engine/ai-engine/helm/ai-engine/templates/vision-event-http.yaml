{{- if .Values.visionEventHttp.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ai-engine.fullname" $ }}-vision-event-http
  labels:
    {{- include "ai-engine.labels" $ | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.visionEventHttp.replicaCount }}
  {{- end }}
  strategy:
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels:
      {{- include "ai-engine.selectorLabels" . | nindent 6 }}
      "deploymentPod": "vision-event-http"
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "ai-engine.selectorLabels" . | nindent 8 }}
        "deploymentPod": "vision-event-http"
    spec:
      {{- with .Values.nonGpuNodeAffinity }}
      affinity:
        nodeAffinity:
        {{- toYaml . | nindent 10 }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.serviceAccount.create }}
      serviceAccountName: {{ include "ai-engine.serviceAccountName" . }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      volumes:
        - name: conf
          projected:
            sources:
              - configMap:
                  name: {{ include "gateway.conf" . }}
              - configMap:
                  name: {{ include "ai-engine.fullname" . }}-conf
        - name: secrets
          projected:
            sources:
            - secret:
                name: rds-read-auth
                items:
                  - key: username
                    path: rds_read_username
                  - key: password
                    path: rds_read_password
                  - key: host
                    path: rds_read_host
                  - key: database
                    path: rds_read_database
            - secret:
                name: rds-auth
                items:
                  - key: username
                    path: rds_username
                  - key: password
                    path: rds_password
                  - key: host
                    path: rds_host
                  - key: database
                    path: rds_database
            - secret:
                name: aws-auth
                items:
                  - key: access_key_id
                    path: aws_access_key_id
                  - key: secret_key
                    path: aws_secret_key
      terminationGracePeriodSeconds: {{ .Values.visionEventHttp.terminationGracePeriodSeconds | default 30 }}
      containers:
        - name: vision-event-http
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.visionEventHttp.image.repository }}:{{ .Values.visionEventHttp.image.tag | default .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: HTTP_WORKERS
              value: "{{ .Values.visionEventHttp.httpWorkers | default 2 }}"
          ports:
            - containerPort: 8080
              name: http
            - name: metrics-veh
              containerPort: 8000
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /readiness
              port: http
            initialDelaySeconds: 5
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: http
            initialDelaySeconds: 15
            periodSeconds: 20
          volumeMounts:
            - name: secrets
              mountPath: /secrets
              readOnly: true
            - name: conf
              mountPath: /conf
              readOnly: true
          resources:
            {{- toYaml .Values.visionEventHttp.resources | nindent 12 }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "ai-engine.fullname" $ }}-vision-event-http
  labels:
    {{- include "ai-engine.labels" $ | nindent 4 }}
spec:
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "ai-engine.selectorLabels" . | nindent 4 }}
    "deploymentPod": "vision-event-http"
{{- end }} 
