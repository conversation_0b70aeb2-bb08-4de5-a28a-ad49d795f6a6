{{- if .Values.components.acsAlarmRunner -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "hip.fullname" . }}-acs-alarm-runner
  labels:
    app.kubernetes.io/component: acs-alarm-runner
    {{- include "hip.labels" . | nindent 4 }}
  annotations:
    checksum/config: {{ include (print $.Template.BasePath "/config.yaml") . | sha256sum }}
spec:
  replicas: 1
  strategy:
    # singleton. so disable surge
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: acs-alarm-runner
      {{- include "hip.selectorLabels" $ | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- with .Values.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        checksum/config: {{ include (print $.Template.BasePath "/config.yaml") . | sha256sum }}
      labels:
        app.kubernetes.io/component: acs-alarm-runner
        {{- include "hip.labels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "hip.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      volumes:
        - name: secure
          secret:
            secretName: hip-secure
        - name: certs
          secret:
            secretName: client-certs
        # todo: remove conf
        - name: conf
          configMap:
            name: {{ include "hip.fullname" . }}-conf
        - name: data
          persistentVolumeClaim:
            claimName: {{ include "hip.fullname" . }}-data
            readOnly: false
      {{- with .Values.nonGpuNodeAffinity }}
      affinity:
        nodeAffinity:
        {{- toYaml . | nindent 10 }}
      {{- end }}
      containers:
        - name: acs-alarm-runner
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
            - mountPath: /secure
              name: secure
            - mountPath: /secure/certs
              name: certs
            - mountPath: /conf
              name: conf
            - mountPath: /data
              name: data
          command:
            - python
          args:
            - apps/acs_alarm_runner.py
          ports:
            - name: http
              containerPort: 9001
              protocol: TCP
            - name: acs-alm-met
              containerPort: 8800
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
---
{{- if .Values.components.acsAlarmListenerService.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "hip.fullname" . }}-acs-alm-listn
  labels:
    {{- include "hip.labels" . | nindent 4 }}
spec:
  # https://kubernetes.io/docs/concepts/services-networking/service/#type-nodeport
  type: NodePort
  ports:
    - port: 9001
      targetPort: http
      protocol: TCP
      nodePort: {{ .Values.components.acsAlarmListenerService.nodePort }}
  selector:
    {{- include "hip.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: acs-alarm-runner
{{- end }} 
{{- end -}}