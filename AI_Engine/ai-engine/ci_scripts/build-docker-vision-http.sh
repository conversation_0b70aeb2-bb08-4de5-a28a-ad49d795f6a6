#!/bin/bash
set -eo pipefail

if [ "$#" -gt 2 ];
then
    echo "Usage bash build-docker-vision-http.sh <OPTIONAL branch name> <OPTIONAL TAG>"
    exit 126
fi

BASE_DOCKER_IMG=$1

if [ branch=$(git branch --show-current 2>/dev/null) ];
then
    # git version is old
    branch=$(git rev-parse --abbrev-ref HEAD)
fi

BRANCH=${2:-$branch}
echo "Branch for vision http build" >&2
echo $BRANCH >&2
ECR_REGISTRY="695273141991.dkr.ecr.us-west-2.amazonaws.com"
ECR_REPO="hakimo-vision/http-server"
aws ecr get-login-password --region us-west-2 | \
    docker login --username AWS --password-stdin $ECR_REGISTRY

COMMIT_SHA=$(git rev-parse HEAD)
echo "Commit for vision http build" >&2
echo $COMMIT_SHA >&2

TAG_PREFIX=$( [ $BRANCH == 'master' ] && echo "master" || echo "dev" )

{ docker build \
    -f Dockerfile.vision.http \
    -t $ECR_REGISTRY/$ECR_REPO:$TAG_PREFIX-$COMMIT_SHA . ; } >&2
if [ $BRANCH == "master" ];
then
    docker push $ECR_REGISTRY/$ECR_REPO:$TAG_PREFIX-$COMMIT_SHA
    docker tag $ECR_REGISTRY/$ECR_REPO:$TAG_PREFIX-$COMMIT_SHA $ECR_REGISTRY/$ECR_REPO:latest
    docker push $ECR_REGISTRY/$ECR_REPO:latest
fi

printf "\n\n"
echo $ECR_REGISTRY/$ECR_REPO:$TAG_PREFIX-$COMMIT_SHA
if [ $BRANCH == "master" ];
then
    echo $ECR_REGISTRY/$ECR_REPO:latest
fi

TAG=$2
if [ -n "$TAG" ];
then
    echo "Tag used" >&2
    echo $TAG >&2
    docker tag $ECR_REGISTRY/$ECR_REPO:$TAG_PREFIX-$COMMIT_SHA $ECR_REGISTRY/$ECR_REPO:$TAG
    docker push $ECR_REGISTRY/$ECR_REPO:$TAG
    echo $ECR_REGISTRY/$ECR_REPO:$TAG
fi

