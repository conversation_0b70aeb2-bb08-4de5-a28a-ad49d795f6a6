---
- name: Checkout 'deploy' repo
  ansible.builtin.git:
    repo: **************:hakimo-ai/deploy.git
    dest: "{{ tmp_folder_path }}/{{ tenant_id }}/"
    accept_newhostkey: true
    clone: true
    force: true
    version: master

- name: Create a new branch
  ansible.builtin.shell: "git checkout {{ tenant_id }} || git checkout -b {{ tenant_id }}"
  args:
    chdir: "{{ tmp_folder_path }}/{{ tenant_id }}/"

- name: Generate files
  ansible.builtin.template:
    src: "{{ item.template }}"
    dest: "{{ item.file }}"
    mode: 0644
  loop:
    - { template: 'templates/hip-motion/kustomization.j2', file: "{{ tmp_folder_path }}/{{ tenant_id }}/tenants/{{ tenant_name }}/hip-motion/kustomization.yaml" }
    - { template: 'templates/hip-motion/hip-values.j2', file: "{{ tmp_folder_path }}/{{ tenant_id }}/tenants/{{ tenant_name }}/hip-motion/hip-values.yaml" }
  tags:
    - test-templates-files
