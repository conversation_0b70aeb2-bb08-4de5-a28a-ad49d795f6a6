---

- name: Install ffmpeg (provides ffprobe) on macOS
  homebrew:
    name: ffmpeg
    state: present
  when: ansible_facts['os_family'] == "Darwin"

- name: Build complete camera list from all hosts
  set_fact:
    all_cameras: "{{ all_cameras | default([]) + hostvars[item].cameras | default([]) }}"
  loop: "{{ ansible_play_hosts }}"

- name: Run ffprobe on macOS
  command: >-
    /opt/homebrew/bin/ffprobe -v quiet -print_format json -show_streams {{ item.rtsp_url }}
  loop: "{{ all_cameras }}"
  register: ffprobe_results_darwin
  ignore_errors: true
  when: ansible_facts['os_family'] == "Darwin"

- name: Run ffprobe on Linux
  command: >-
    ffprobe -v quiet -print_format json -show_streams {{ item.rtsp_url }}
  loop: "{{ all_cameras }}"
  register: ffprobe_results_linux
  ignore_errors: true
  when: ansible_facts['os_family'] != "Darwin"

- name: Combine ffprobe results
  set_fact:
    ffprobe_results: >-
      {{
        ffprobe_results_darwin.results
        if ansible_facts['os_family'] == "Darwin"
        else ffprobe_results_linux.results
      }}

- name: Build list of camera metadata
  set_fact:
    parsed_camera_metadata: >-
      {{
        parsed_camera_metadata | default([]) + [ {
          'id': item.item.id,
          'name': item.item.name,
          'rtsp_url': item.item.rtsp_url,
          'codec': (
            (item.stdout | from_json).streams
            | selectattr('codec_type', 'equalto', 'video')
            | map(attribute='codec_name')
            | list | first | default('N/A')
          ),
          'resolution': (
            ((item.stdout | from_json).streams
            | selectattr('codec_type', 'equalto', 'video')
            | map(attribute='width') | list | first | default('?') | string)
            + 'x' +
            ((item.stdout | from_json).streams
            | selectattr('codec_type', 'equalto', 'video')
            | map(attribute='height') | list | first | default('?') | string)
          )
        } ]
      }}
  loop: "{{ ffprobe_results }}"
  when: item.rc is defined and item.rc == 0 and item.stdout is defined and item.stdout | length > 0
  no_log: true

- name: Write camera metadata to file
  copy:
    dest: /tmp/camera_metadata.json
    content: |
      [
      {%- for cam in parsed_camera_metadata | default([]) %}
        {
          "id": "{{ cam.id }}",
          "name": "{{ cam.name }}",
          "rtsp_url": "{{ cam.rtsp_url }}",
          "codec": "{{ cam.codec }}",
          "resolution": "{{ cam.resolution }}"
        }{{ "," if not loop.last else "" }}
      {%- endfor %}
      ]

- name: Print camera metadata as JSON
  command: cat /tmp/camera_metadata.json
  register: camera_metadata_output

- name: Show camera metadata
  debug:
    msg: "{{ camera_metadata_output.stdout }}"
