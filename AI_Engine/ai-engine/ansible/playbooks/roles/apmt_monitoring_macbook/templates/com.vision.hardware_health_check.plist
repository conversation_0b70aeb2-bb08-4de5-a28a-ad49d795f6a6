<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.vision.hardware_health_check</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>/Users/<USER>/opt/apps/vision/scripts/hardware_health_check.sh</string>
        <string>/Users/<USER>/opt/conf/cameras.yaml</string>
        <string>/Users/<USER>/opt/conf/.env</string>
        <string>300</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/opt/logs/hardware_health_check_error.log</string>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/opt/logs/hardware_health_check_output.log</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/opt/homebrew/bin</string>
    </dict>
</dict>
</plist>
