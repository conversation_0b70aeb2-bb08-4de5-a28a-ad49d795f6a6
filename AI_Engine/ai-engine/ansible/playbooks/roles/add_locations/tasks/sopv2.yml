- name: Enable SOP for multiple locations
  delegate_to: localhost
  tags:
    - sop2
    - new-cams
    - new-locations
  block:
    - name: Check if location {{ location_item.name }} exists
      community.mysql.mysql_query:
        login_host: "{{ rds_host }}"
        login_user: "{{ rds_username }}"
        login_password: "{{ rds_password }}"
        login_db: "{{ rds_database }}"
        login_port: "{{ rds_port }}"
        query: >
          SELECT id FROM `locations`
          WHERE tenant_id = '{{ tenant_id }}'
          AND name = '{{ location_item.name }}'
      register: location_status

    - name: Set Tenant Location Monitoring Config fact
      set_fact:
        location_monitoring_config:
          MOTION_DISARM_WINDOW:
          MOTION_DETECTION_WINDOW:
            timezone: "{{ location_item.timezone }}"
            day_group_ranges:
              - ranges:
                  - order: 0
                    end_at_tz: '00:01:00'
                    start_at_tz: '00:00:00'
                days_of_week_tz:
                  - SUNDAY
                  - MONDAY
                  - TUESDAY
                  - WEDNESDAY
                  - THURSDAY
                  - FRIDAY
                  - SATURDAY

    - name: Create the location {{ location_item.name }}
      community.mysql.mysql_query:
        login_host: "{{ rds_host }}"
        login_user: "{{ rds_username }}"
        login_password: "{{ rds_password }}"
        login_db: "{{ rds_database }}"
        login_port: "{{ rds_port }}"
        query: >
          INSERT INTO `locations` (tenant_id, name, country, state, city, timezone, description, monitoring_config)
          VALUES ('{{ tenant_id }}', '{{ location_item.name }}', '{{ location_item.country }}',
          '{{ location_item.state }}', '{{ location_item.city }}', '{{ location_item.timezone }}',
          '{{ location_item.description }}', '{{ location_monitoring_config | to_json }}')
      when: location_status.query_result | length == 1 and location_status.query_result[0] | length == 0

    - name: Retrieve the location id for {{ location_item.name }}
      community.mysql.mysql_query:
        login_host: "{{ rds_host }}"
        login_user: "{{ rds_username }}"
        login_password: "{{ rds_password }}"
        login_db: "{{ rds_database }}"
        login_port: "{{ rds_port }}"
        query: >
          SELECT id FROM `locations`
          WHERE tenant_id = '{{ tenant_id }}'
          AND name = {{ location_item.name | default('') | quote }}
      register: location_info

    - name: Check if SOP is enabled for location {{ location_item.name }}
      community.mysql.mysql_query:
        login_host: "{{ rds_host }}"
        login_user: "{{ rds_username }}"
        login_password: "{{ rds_password }}"
        login_db: "{{ rds_database }}"
        login_port: "{{ rds_port }}"
        query: SELECT id FROM `sop` WHERE tenant_id = '{{ tenant_id }}' and location_id = {{ location_info.query_result.0.0.id }}
      register: sop_status

    - name: Enable SOP for the tenant and location {{ location_item.name }}
      community.mysql.mysql_query:
        login_host: "{{ rds_host }}"
        login_user: "{{ rds_username }}"
        login_password: "{{ rds_password }}"
        login_db: "{{ rds_database }}"
        login_port: "{{ rds_port }}"
        query: >
          INSERT INTO `sop` (tenant_id, sop, location_id)
          VALUES ('{{ tenant_id }}', 'Enable SOP', {{ location_info.query_result.0.0.id }})
      when:
        - sop_status.query_result | length == 1 and sop_status.query_result[0] | length == 0
