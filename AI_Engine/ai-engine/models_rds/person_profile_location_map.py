import datetime

import sqlalchemy as sa
from sqlalchemy import Column, Foreign<PERSON>ey, String, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.types import DateTime

from models_rds.locations import Locations
from models_rds.person_profile import PersonProfiles
from models_rds.rds_base import RDSBase


class PersonProfileLocationMap(RDSBase):
    __tablename__ = "person_profile_location_map"

    person_profile_id = Column(
        "person_profile_id",
        String(36),
        ForeignKey(PersonProfiles.id),
        primary_key=True,
        nullable=False,
    )

    location_id = Column(
        "location_id",
        sa.BigInteger().with_variant(sa.Integer, "sqlite"),
        ForeignKey(Locations.id),
        primary_key=True,
        nullable=False,
    )

    is_active = Column("is_active", sa.BOOLEAN)
    created_at_utc = Column(
        "created_at_utc", DateTime(timezone=True), server_default=func.utcnow()
    )

    updated_at_utc = Column(
        "updated_at_utc",
        DateTime(timezone=True),
        server_default=func.utcnow(),
        onupdate=datetime.datetime.utcnow,
    )
    __table_args__ = (
        UniqueConstraint(
            "person_profile_id",
            "location_id",
            name="person_profile_loc_constraint",
        ),
    )
