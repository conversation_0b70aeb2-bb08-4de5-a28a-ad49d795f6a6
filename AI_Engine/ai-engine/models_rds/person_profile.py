"""
Specific table in the RDS
Note how it inherits from Base
Individual columns are defined, and internally mapped to the actual
column in the table by sqlalchemy
"""

import datetime

import sqlalchemy as sa
import structlog
from sqlalchemy import (
    JSON,
    Column,
    String,
)
from sqlalchemy.sql import func
from sqlalchemy.types import DateTime

from models_rds.rds_base import RDSBase

log = structlog.get_logger("hakimo.models_rds", module="PersonProfile Model")


class PersonProfiles(RDSBase):
    """
    Class corresponding to PersonProfile table in RDS
    """

    __tablename__ = "person_profiles"

    id = Column("id", String(36), primary_key=True, nullable=False)
    primary_image_url = Column("primary_image_url", String(256), default=None)
    images_metadata = Column("images_metadata", JSON, default=None)
    first_name = Column("first_name", String(36), default=None)
    last_name = Column("last_name", String(36), default=None)
    age = Column(
        "age",
        sa.Integer().with_variant(sa.Integer, "sqlite"),
    )
    gender = Column("gender", String(10), default=None)
    tags = Column("tags", JSON, default=None)
    misc_info = Column("misc_info", JSON, default=None)
    employee_id = Column("employee_id", String(36), default=None)
    watch_enabled = Column("watch_enabled", sa.BOOLEAN)
    is_enabled = Column("is_enabled", sa.BOOLEAN)
    tenant_id = Column(String(36), default=None)
    created_at_utc = Column(
        "created_at_utc", DateTime(timezone=True), server_default=func.utcnow()
    )
    updated_at_utc = Column(
        "updated_at_utc",
        DateTime(timezone=True),
        server_default=func.utcnow(),
        onupdate=datetime.datetime.utcnow,
    )

    def __repr__(self):
        return "<Person Profile {}>".format(self.uuid)
