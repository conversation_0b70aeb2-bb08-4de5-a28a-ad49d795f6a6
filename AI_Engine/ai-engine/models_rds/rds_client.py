"""# pylint: disable=too-many-lines  # pylint: disable=too-many-lines
Classes to implement ORM for tables in RDS MySQL Database with SQLAlachemy.
"""

import datetime
import time
import typing
import uuid
from typing import Any, Dict, Optional

import numpy as np
import structlog
from sqlalchemy import select
from sqlalchemy.dialects import mysql

import controller as ctrl
import errors
from common_utils.bridge.msg_sender import MsgSender
from common_utils.db_pool import db_adapter_pool, read_db_adapter_pool
from common_utils.mapping_utils import get_camera_info_dict
from common_utils.metrics_definitions import (
    MYSQL_READ_SUMMARY,
    MYSQL_WRITE_SUMMARY,
)
from database.db_adapter import DBAdapter
from interfaces.alarm import ANALYZING_STATUS, Alarm
from interfaces.scene_info import SceneInfo
from interfaces.source_systems import SourceEntityType
from models_rds.alarm_types import AlarmTypes
from models_rds.cameras import Cameras
from models_rds.door_camera_params import DoorCameraParams
from models_rds.doors import Doors
from models_rds.employees import Employees
from models_rds.issues import Issues
from models_rds.issues_alarms_junction_table import IssuesAlarmsJunctionTable
from models_rds.raw_alarms import RawAlarms, get_default_partition_key_filter

log = structlog.get_logger("hakimo", module="RDS Client")


def debug_query_performance(query, description="Query"):
    """
    Utility function to print the actual SQL query for performance analysis.

    Args:
        query: SQLAlchemy query object
        description: Description of the query for logging

    Usage:
        debug_query_performance(query, "Get alarm properties at door")

    The output can be copied and used with EXPLAIN in MySQL for performance analysis.
    """
    try:
        compiled_query = query.statement.compile(
            dialect=mysql.dialect(),
            compile_kwargs={"literal_binds": True}
        )
        print(f"\n=== {description} ===")
        print("SQL Query for performance analysis:")
        print(compiled_query)
        print("=" * (len(description) + 8))
        print("\nTo analyze performance:")
        print("1. Copy the SQL above")
        print("2. In MySQL, run: EXPLAIN FORMAT=JSON <your_sql_here>")
        print("3. Look for table scans, missing indexes, and high costs\n")
    except Exception as e:
        log.error(f"Failed to compile query for debugging: {e}")


class RDSClient:  # pylint: disable=too-many-public-methods
    """
    Class containing all implementation to interface with RDS
    """

    def __init__(
        self,
        db_adapter: DBAdapter = None,
        read_db_adapter: typing.Optional[DBAdapter] = None,
    ):
        # Write db adapter instance as an instance variable
        if db_adapter is None:
            self.db_adapter = db_adapter_pool
        else:
            self.db_adapter = db_adapter

        # Read db adapter instance as an instance variable
        if read_db_adapter is None:
            self.read_db_adapter = read_db_adapter_pool
        else:
            self.read_db_adapter = read_db_adapter
        # TODO: this annotation can be Dict[str,Any] too.
        # But looks keys can be Enum too, hence keeping as [Any, Any]
        self.cache_alarm_types: Dict[Any, Any] = {}
        self.cache_alarm_type_names: Dict[str, Any] = {}
        self.regex_alarm_type_cache: Dict[str, Any] = {}
        self._ctrl = ctrl.ControllerMap(
            self.db_adapter, read_db_adapter=self.read_db_adapter
        )
        self._msg_sender = MsgSender(self._ctrl.msg)

    def flush_cache(self):
        self.cache_alarm_types = {}
        self.cache_alarm_type_names = {}
        self.regex_alarm_type_cache = {}

    def get_alarm_type_id_from_alarm_type(
        self, alarm_type, regex=False, tenant_id: typing.Optional[str] = None
    ):
        """
        Get first alarm type ID based on the alarm type
        """
        return self._ctrl.alarm_types.get_alarm_type_id_from_alarm_type(
            alarm_type, regex, tenant_id=tenant_id
        )

    def write_update_raw_alarms(self, alarm, status: str = ANALYZING_STATUS):
        """Insert/Update raw alarms into the RDS,
        with source_id unique constraint handling to get the alarm_id in case alarm already exists

        Args:
            alarm ([Alarm]): Alarm object that is being added to the db
            status ([str]): To update current status for alarm: default Analyzing status
        Returns:
            [alarm_id]: Alarm Id that gets updated or inserted in the db
        """
        try:
            alarm_type_uuid = self.get_alarm_type_id_from_alarm_type(
                alarm.alarm_type, tenant_id=alarm.tenant_id
            )
            (
                empl_id,
                is_new_empl,
            ) = self._ctrl.employee.get_employee_uuid_for_alarm(alarm)
            if is_new_empl:
                log.info(
                    "Source employee ID not found. Triggering cardholder sync.",
                    source_employee_id=alarm.source_employee_id,
                )
                # Send at most one message every 10 minutes.
                thresh = datetime.datetime.utcnow() - datetime.timedelta(
                    seconds=600
                )
                sent = self._msg_sender.send(
                    alarm.tenant_id,
                    f"hip/{alarm.source_system}/cardholder_sync",
                    {},
                    threshold_utc=thresh,
                )
                log.info(
                    "Cardholder sync message send attempted", success=sent
                )
            raw_alarm_rds = RawAlarms(
                uuid=str(uuid.uuid4()),
                alarm_timestamp_utc=alarm.alarm_time,
                alarm_timestamp_local=alarm.alarm_local_time,
                door_id=alarm.door_uuid,
                source_id=alarm.source_id,
                alarm_type_id=alarm_type_uuid,
                source_system=alarm.source_system,
                tenant_id=alarm.tenant_id,
                raw_source_data=alarm.raw_info,
                employee_id=empl_id,
                alarm_state=alarm.state.name,
                video_available=alarm.video_available,
                hakimo_correlation_id=alarm.hakimo_correlation_id,
                current_status=status,
                trace_data=alarm.trace_data,
                source_entity_id=alarm.source_entity_id,
                source_entity_type=alarm.source_entity_type,
                last_motion_event_timestamp_utc=alarm.last_motion_event_timestamp_utc,
                display=alarm.display,
            )
            start = time.time()
            raw_alarm_uuid = self.db_adapter.save_object(
                raw_alarm_rds, "raw_alarms_v2"
            )
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_WRITE_SUMMARY.labels(
                tenant=alarm.tenant_id,
                query="insert_raw_alarms",
                table_name="raw_alarms_v2",
            ).observe(time_taken_ms)
            log.debug(
                "Inserted raw_alarms_v2 table with AlarmId: %s",
                raw_alarm_uuid,
            )
            alarm.set_alarm_uuid(raw_alarm_uuid)
            return raw_alarm_uuid
        except errors.EntityExistsError as exists_err:
            existing_alarm_id = self._ctrl.alarm.alarm_id_by_source_id(
                alarm.source_id, alarm.tenant_id
            )
            alarm.set_alarm_uuid(existing_alarm_id)
            if existing_alarm_id is None:
                log.error(
                    "Alarm with same source ID exists, but not found in RDS",
                    alarm_id=existing_alarm_id,
                )
                raise Exception(
                    "Alarm with same source ID exists, but not found in RDS"
                ) from exists_err
            raise exists_err
        except errors.RetryableError as retry_e:
            log.error(retry_e)
            raise retry_e
        except errors.DbError as db_err:
            log.error(db_err)
            raise db_err

    def update_raw_alarms_with_video_metadata(
        self,
        raw_alarm_uuid: str,
        video_start_datetime_utc: typing.Optional[datetime.datetime],
        video_end_datetime_utc: typing.Optional[datetime.datetime],
        video_path: typing.Optional[str],
        media_id: typing.Optional[int] = None,
    ) -> typing.Optional[RawAlarms]:
        """
        If alarm type requires video, we update raw alarm table
        with video info after it is available
        """
        with self.db_adapter.get_session() as sess:
            try:
                raw_alarm = (
                    sess.execute(
                        select(RawAlarms)
                        .filter(RawAlarms.uuid == raw_alarm_uuid)
                        .filter(get_default_partition_key_filter())
                    )
                    .scalars()
                    .one()
                )
                if video_start_datetime_utc is not None:
                    raw_alarm.video_start_timestamp_utc = (
                        video_start_datetime_utc
                    )
                if video_end_datetime_utc is not None:
                    raw_alarm.video_end_timestamp_utc = video_end_datetime_utc
                if video_path is not None:
                    raw_alarm.video_path = video_path
                if media_id is not None:
                    raw_alarm.latest_alarm_media_id = media_id
                raw_alarm.video_available = True
                sess.commit()
                log.debug(
                    "Updated raw_alarms table with video metadata",
                    alarm_id=raw_alarm_uuid,
                    video_start_time_utc=video_start_datetime_utc,
                    video_end_time_utc=video_end_datetime_utc,
                    video_path=video_path,
                )
                sess.refresh(raw_alarm)
                sess.expunge_all()
                return raw_alarm
            except AttributeError as err:
                log.exception(err, exc_info=err)
                log.error("Raw Alarm uuid not found in raw_alarms table")
                return None

    def get_alarm_object(
        self,
        alarm_id: str,
        tenant_id: str,
        populate_ml_process_info: bool = True,
    ) -> typing.Optional[Alarm]:
        raw_alarm = self._ctrl.alarm.by_id(alarm_id, tenant_id)
        if raw_alarm is None:
            return None
        return self.get_alarm_object_from_raw_alarm(
            raw_alarm, populate_ml_process_info
        )

    def get_alarm_object_from_raw_alarm(
        self, raw_alarm: RawAlarms, populate_ml_process_info: bool = True
    ) -> Alarm:
        alarm_type = (
            self._ctrl.alarm_types.get_alarm_type_name_from_alarm_type_id(
                raw_alarm.alarm_type_id
            )
        )
        employee_id = raw_alarm.employee_id
        employee: Optional[Employees] = None
        source_employee_id: Optional[str] = None
        if employee_id is not None:
            employee = self._ctrl.employee.get_employee_object(  # pylint: disable=unexpected-keyword-arg
                employee_id, refresh_cache=False
            )
            source_employee_id = (
                employee.source_employee_id if employee else None
            )
            if employee_id is not None and source_employee_id is None:
                raise ValueError("Inconsistent alarm employee ID")
        alarm = Alarm(
            alarm_uuid=raw_alarm.uuid,
            alarm_time=raw_alarm.alarm_timestamp_utc,
            tenant_id=raw_alarm.tenant_id,
            alarm_type=alarm_type,
            door_uuid=raw_alarm.door_id,
            video_path=raw_alarm.video_path,
            video_start_time_utc=raw_alarm.video_start_timestamp_utc,
            video_end_time_utc=raw_alarm.video_end_timestamp_utc,
            processing_start_time_utc=raw_alarm.processing_start_timestamp_utc,
            processing_end_time_utc=raw_alarm.processing_end_timestamp_utc,
            source_id=raw_alarm.source_id,
            raw_info=raw_alarm.raw_source_data,
            current_status=raw_alarm.current_status,
            employee_id=raw_alarm.employee_id,
            source_employee_id=source_employee_id,
            source_system=raw_alarm.source_system,
            video_available=raw_alarm.video_available,
            hakimo_correlation_id=raw_alarm.hakimo_correlation_id,
            trace_data=raw_alarm.trace_data,
            source_entity_id=raw_alarm.source_entity_id,
            source_entity_type=raw_alarm.source_entity_type,
            last_motion_event_timestamp_utc=raw_alarm.last_motion_event_timestamp_utc,
            created_at_utc=raw_alarm.created_at_utc,
        )
        if populate_ml_process_info:
            if alarm.source_entity_type == SourceEntityType.DOOR.value:
                door_id = raw_alarm.door_id
                door_camera_info = (
                    self._ctrl.door_camera_params.get_door_camera_mapping(
                        raw_alarm.tenant_id, door_id=door_id
                    )
                )
                if door_camera_info is not None:
                    alarm.scene_info = SceneInfo(
                        door_bbox=door_camera_info.door_coordinates_in_cam_frame,
                        door_orientation_point=door_camera_info.door_orientation_point,
                        camera_position=door_camera_info.camera_position,
                        labelling_resolution=door_camera_info.labelling_resolution,
                        dead_zones=door_camera_info.dead_zones,
                    )
                    camera_info = get_camera_info_dict(door_camera_info)
                    assert (
                        camera_info is not None
                    ), "No camera info for existing door camera mapping"
                    alarm.set_camera_info(camera_info)
                    alarm.process_people = (
                        door_camera_info.doors_info.process_people
                    )
                    alarm.process_vehicles = (
                        door_camera_info.doors_info.process_vehicles
                    )
            elif alarm.source_entity_type == SourceEntityType.CAMERA.value:
                assert (
                    alarm.source_entity_id is not None
                ), "Alarm source camera must have source entity ID set"
                camera = self._ctrl.camera.get_camera_by_id(
                    alarm.source_entity_id, alarm.tenant_id
                )
                if camera is not None:
                    motion_contours = None
                    if (
                        raw_alarm.raw_source_data is not None
                        and isinstance(raw_alarm.raw_source_data, dict)
                        and "motion_contours" in raw_alarm.raw_source_data
                    ):
                        motion_contours = raw_alarm.raw_source_data.get(
                            "motion_contours", None
                        )
                        if motion_contours is not None and isinstance(
                            motion_contours, list
                        ):
                            motion_contours = [
                                np.array(c).reshape(-1, 2).tolist()
                                for c in motion_contours
                            ]

                    alarm.scene_info = SceneInfo(
                        labelling_resolution=camera.labelling_resolution,
                        dead_zones=camera.motion_dead_zones,
                        vehicle_parking_zones=camera.vehicle_parking_zones,
                        active_zones=camera.active_zones,
                        motion_contours=motion_contours,
                    )
                    # Motion alarms always need to process people
                    # We can add a new field in teh cameras table if we would like
                    # to turn it on/off
                    alarm.process_people = True
                    alarm.process_vehicles = camera.process_vehicles
                    source_id = (
                        camera.camera_raw_info["source_id"]
                        if "source_id" in camera.camera_raw_info
                        else 1
                    )
                    camera_info = {
                        "camera_name": camera.name,
                        "client_camera_id": camera.client_camera_id,
                        # In vms integration the key is all upper case.
                        "camera_integration_type": camera.integration_type,
                        "cam_site_id": camera.cam_site_id,
                        "source_id": source_id,
                    }
                    alarm.set_camera_info(camera_info)
                    alarm.is_thermal = camera.is_thermal
        return alarm

    def write_to_issues_table(self, door_uuid, issue_root_cause, description):
        """
        Utility method to insert found issues into appropriate RDS table
        """
        new_uuid = str(uuid.uuid4())
        issues = Issues(
            uuid=new_uuid,
            root_cause=issue_root_cause,
            door_id=door_uuid,
            issue_description=description,
        )
        self.db_adapter.save_object(issues, "issues")

        return new_uuid

    def write_to_issues_alarms_junction_table(self, issue_uuid, alarm_uuid):
        """
        Adds to junction table to maintain a mapping between issues and
        alarms tied to that issue
        """
        new_uuid = str(uuid.uuid4())
        issue_alarm = IssuesAlarmsJunctionTable(
            uuid=new_uuid, issue_id=issue_uuid, alarm_id=alarm_uuid
        )
        self.db_adapter.save_object(
            issue_alarm, "issues_alarms_junction_table"
        )

    def get_raw_alarm_ids_at_door(
        self, door_uuid, start_time, end_time, alarm_type_id=None
    ):
        """
        Given a doorid, get raw alarms at the door between
        start and end time
        If alarm_type_id is not None, only return alarms of that type
        """
        with self.db_adapter.get_session() as sess:
            query = (
                select(RawAlarms.uuid)
                .filter(RawAlarms.door_id == door_uuid)
                .filter(
                    (RawAlarms.alarm_timestamp_utc).between(
                        start_time, end_time
                    )
                )
                .filter(get_default_partition_key_filter())
            )
            if alarm_type_id is not None:
                query = query.filter(RawAlarms.alarm_type_id == alarm_type_id)

            alarm_ids = sess.execute(query).scalars().all()
        return alarm_ids

    def get_alarm_times_at_door(
        self,
        door_uuid: str,
        start_time: datetime.datetime,
        end_time: typing.Optional[datetime.datetime] = None,
        alarm_type_id: typing.Union[typing.Sequence[str], str] = None,
    ) -> typing.List[datetime.datetime]:
        """
        Given a doorid, get raw alarm times at the door between
        start and end time
        If alarm_type_id is not None, only return alarms of that type
        """
        if end_time is None:
            end_time = datetime.datetime.max
        with self.db_adapter.get_session() as sess:
            query = (
                select(RawAlarms.alarm_timestamp_utc)
                .filter(RawAlarms.door_id == door_uuid)
                .filter(
                    (RawAlarms.alarm_timestamp_utc).between(
                        start_time, end_time
                    )
                )
                .filter(get_default_partition_key_filter())
            )
            if isinstance(alarm_type_id, (list, tuple)):
                query = query.filter(
                    RawAlarms.alarm_type_id.in_(alarm_type_id)
                )
            elif alarm_type_id is not None:
                query = query.filter(RawAlarms.alarm_type_id == alarm_type_id)

            # unpack the tuple of ids from sqlalchemy row
            alarm_times = sess.execute(query).scalars().all()
        return alarm_times

    def get_alarm_properties_at_door(
        self,
        properties: typing.List[str],
        door_uuid: str,
        tenant_id: str,
        start_time: datetime.datetime,
        end_time: typing.Optional[datetime.datetime] = None,
        alarm_type_id: typing.Union[typing.Sequence[str], str] = None,
    ) -> typing.List[typing.Sequence]:
        """
        Given a doorid, get alarm properties at the door between
        start and end time
        If alarm_type_id is not None, only return alarms of that type
        """
        if end_time is None:
            end_time = datetime.datetime.max
        query = (
            select(*[getattr(RawAlarms, property) for property in properties])
            .filter(RawAlarms.door_id == door_uuid)
            .filter(
                (RawAlarms.alarm_timestamp_utc).between(start_time, end_time)
            )
            .filter(RawAlarms.tenant_id == tenant_id)
            .filter(get_default_partition_key_filter())
        )
        with self.db_adapter.get_session() as sess:
            if isinstance(alarm_type_id, (list, tuple)):
                query = query.filter(
                    RawAlarms.alarm_type_id.in_(alarm_type_id)
                )
            elif alarm_type_id is not None:
                query = query.filter(RawAlarms.alarm_type_id == alarm_type_id)
            alarms_row = sess.execute(query).all()
            property_data: typing.List[typing.Sequence]
            property_data = [() for property in properties]
            if len(alarms_row) > 0:
                property_data = list(zip(*alarms_row))
        return property_data

    def get_alarm_times_employees_at_door(
        self,
        door_uuid: str,
        tenant_id: str,
        start_time: datetime.datetime,
        end_time: typing.Optional[datetime.datetime] = None,
        alarm_type_id: typing.Union[typing.Sequence[str], str] = None,
    ) -> typing.Tuple[
        typing.Sequence[datetime.datetime], typing.Sequence[str]
    ]:
        ags, employee_ids = self.get_alarm_properties_at_door(
            properties=["alarm_timestamp_utc", "employee_id"],
            door_uuid=door_uuid,
            tenant_id=tenant_id,
            start_time=start_time,
            end_time=end_time,
            alarm_type_id=alarm_type_id,
        )
        return ags, employee_ids

    def get_camera_info_from_id(
        self, camera_id: str, tenant_id: str
    ) -> Cameras:
        """Get camera info from camera ID"""
        with self.db_adapter.get_session() as sess:
            camera_info = (
                sess.execute(
                    select(Cameras)
                    .filter(Cameras.uuid == camera_id)
                    .filter(Cameras.tenant_id == tenant_id)
                )
                .scalars()
                .one()
            )
            sess.expunge_all()
        return camera_info

    def insert_camera(
        self,
        camera_name: str,
        rtsp_url: str,
        tenant_id: str,
        camera_details: typing.Optional[dict] = None,
    ):
        """
        Add camera to RDS database
        """
        log.debug("Adding camera %s at %s", camera_name, rtsp_url)
        with self.db_adapter.get_session() as sess:
            cam_id = (
                sess.execute(
                    select(Cameras.uuid)
                    .filter(Cameras.tenant_id == tenant_id)
                    .filter(Cameras.name == camera_name)
                )
                .scalars()
                .first()
            )
            if cam_id is not None:
                log.warning(
                    "Camera already exists",
                    camera_name=camera_name,
                    tenant_id=tenant_id,
                )
                return cam_id
        cam_uid = str(uuid.uuid4())
        if camera_details is not None:
            height = camera_details.get("height")
            width = camera_details.get("width")
            fps = camera_details.get("fps")
            integration_type = camera_details.get("integration_type", "")
            client_camera_id = camera_details.get("client_camera_id", "")
            camera_timezone = camera_details.get("camera_timezone", "")
            camera_raw_info = camera_details.get("camera_raw_info", {})
        else:
            height = width = fps = None
            integration_type = client_camera_id = camera_timezone = ""
            camera_raw_info = {}
        camera = Cameras(
            uuid=cam_uid,
            name=camera_name,
            rtsp_url=rtsp_url,
            height_pixels=height,
            width_pixels=width,
            fps=fps,
            integration_type=integration_type,
            client_camera_id=client_camera_id,
            camera_timezone=camera_timezone,
            camera_raw_info=camera_raw_info,
            tenant_id=tenant_id,
        )
        self.db_adapter.save_object(camera, "cameras")
        return cam_uid

    def get_door_info_from_door_name(self, door_name: str, tenant_id: str):
        """Gets the door object(s) from RDS with the door name

        Args:
            door_name ([str]): Name of door
            tenant_id ([str]): Tenant ID

        Returns:
            [list]: List of door named tuples from RDS
        """
        doors = self._ctrl.door.get_door_info_from_door_name(
            door_name=door_name, tenant_id=tenant_id
        )
        return doors

    def get_camera_info_from_camera_name(
        self, camera_name: str, tenant_id: str
    ):
        """Gets the camera object(s) from RDS from the camera name

        Args:
            camera_name ([str]): Name of camera
            tenant_id ([str]): Tenant ID

        Returns:
            [list]: List of camera named tuples from RDS
        """
        with self.db_adapter.get_session() as sess:
            cameras = (
                sess.execute(
                    select(Cameras)
                    .filter(Cameras.name == camera_name)
                    .filter(Cameras.tenant_id == tenant_id)
                )
                .scalars()
                .all()
            )
            sess.expunge_all()
        return cameras

    def insert_door_camera_mapping(
        self,
        door_id,
        camera_id,
        tenant_id,
    ):
        """
        Add door camera mapping to RDS database
        """
        self._ctrl.door_camera_params.add_dcp(
            door_id,
            camera_id,
            tenant_id,
        )

    def get_door_id_from_door_details(
        self, door_client_id: typing.Optional[str], tenant_id: str
    ) -> typing.Optional[str]:
        """Given door client_id, get the door ID for the door.

        if door_client_id is None, this method will return None
        (No door in RDS should have a none client ID)
        """
        if door_client_id is None:
            return None
        with self.db_adapter.get_session() as sess:
            query = (
                select(
                    Doors.uuid,
                )
                .filter(Doors.tenant_id == tenant_id)
                .filter(Doors.client_door_id == door_client_id)
            )
            log.info("Looking for door/panel", door_client_id=door_client_id)
            start = time.time()
            row = sess.execute(query).scalars().first()
            time_taken_ms = round((time.time() - start) * 1000)
            MYSQL_READ_SUMMARY.labels(
                tenant=tenant_id,
                query="get_door_id_from_door_details",
                table_name="doors",
            ).observe(time_taken_ms)

        return row

    def get_raw_alarms(
        self,
        tenant_id: Optional[str] = None,
        alarm_list: Optional[typing.List] = None,
        start_time: Optional[datetime.datetime] = None,
        end_time: Optional[datetime.datetime] = None,
        alarm_type: Optional[str] = None,
    ):
        """Get all alarms for a given tenant, that belong to the alarm_list,
        and additionally are after a given start time and before a given end time

        Args:
            tenant_id ([str]): ID of the tenant
            alarm_list ([list], optional): List of alarm IDs to get.
                Defaults to None.
            start_time ([datetime.datetime], optional): Start time for alarms to get.
                Defaults to None.
            end_time ([datetime.datetime], optional): end time for alarms to get.
                Defaults to None.
            alarm_type ([str], optional): type of alarms to retrieve
        Returns:
            [list]: List of alarms satisfying the criteria above
        """
        with self.db_adapter.get_session() as sess:
            query = (
                select(
                    RawAlarms.uuid,
                    RawAlarms.alarm_timestamp_utc,
                    RawAlarms.video_path,
                    RawAlarms.door_id,
                    Doors.door_name,
                    AlarmTypes.alarm_type,
                    RawAlarms.video_start_timestamp_utc,
                    RawAlarms.video_end_timestamp_utc,
                    RawAlarms.alarm_update_id,
                )
                .filter(RawAlarms.door_id == Doors.uuid)
                .filter(RawAlarms.alarm_type_id == AlarmTypes.uuid)
            )
            if tenant_id is None:
                query = query.filter(RawAlarms.tenant_id == tenant_id)

            if alarm_list is not None:
                query = query.filter(RawAlarms.uuid.in_(alarm_list))
            if start_time is not None:
                query = query.filter(
                    RawAlarms.alarm_timestamp_utc >= start_time
                )
            if end_time is not None:
                query = query.filter(RawAlarms.alarm_timestamp_utc < end_time)
            query = query.filter(get_default_partition_key_filter())
            if alarm_type is not None:
                query = query.filter(AlarmTypes.alarm_type == alarm_type)
            raw_alarms = sess.execute(query).all()
        return raw_alarms

    def get_door_info_from_door_id(self, door_id: str):
        """Given the door ID, get the door bounding box
        and camera corresponding to it, along with the camera
        position

        Args:
            door_id ([str]): Unique ID of door

        Returns:
            [tuple]: Tuple containing door bounding box coordinates,
            camera name and the position of the camera.
        """
        door_info = self._ctrl.door_camera_params.get_door_camera_mapping(
            door_id=door_id
        )
        return door_info

    def get_all_door_camera_mappings(
        self, tenant_id: str
    ) -> typing.Sequence[DoorCameraParams]:
        """
        Returns all door camera mappings for a given tenant
        """
        with self.db_adapter.get_session() as sess:
            mappings = (
                sess.execute(
                    select(DoorCameraParams).filter(
                        DoorCameraParams.tenant_id == tenant_id
                    )
                )
                .scalars()
                .all()
            )
            sess.expunge_all()
        return mappings
