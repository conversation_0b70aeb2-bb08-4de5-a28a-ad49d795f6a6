import base64
import json
import mimetypes
import os
import re
import time
import typing
from datetime import datetime

import requests
import structlog

log = structlog.get_logger("hakimo", module="Gemini Alarm Analyzer")


class GeminiAlarmAnalyzer:
    def __init__(
        self,
        api_key: str,
    ):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com"
        self.timeout = 5

    def send_request(
        self,
        request_text: typing.Optional[str],
        video_uri: typing.Optional[str] = None,
        video_bytes: typing.Optional[bytes] = None,
    ) -> typing.Tuple[bool, typing.Dict[str, str], str]:
        request_parts = []
        if request_text:
            request_parts.append({"text": request_text})
        if video_uri:
            request_parts.append(
                {
                    "file_data": {
                        "mime_type": "video/mp4",
                        "file_uri": video_uri,
                    }
                }
            )
        if video_bytes:
            request_parts.append(
                {
                    "inline_data": {
                        "mime_type": "video/mp4",
                        "data": video_bytes,
                    }
                }
            )
        content_request = {
            "contents": [
                {
                    "parts": request_parts,
                }
            ]
        }
        generate_headers = {"Content-Type": "application/json"}
        generate_response = requests.post(
            f"{self.base_url}/v1beta/models/gemini-2.0-flash:generateContent?key={self.api_key}",
            # f"{self.base_url}/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key={self.api_key}",
            headers=generate_headers,
            json=content_request,
        )
        response_data = generate_response.json()
        out = [
            part["text"]
            for candidate in response_data.get("candidates", [])
            for part in candidate.get("content", {}).get("parts", [])
        ]
        full_out = "\n".join(out)
        # Strip leading/trailing spaces and remove markdown markers
        cleaned_output = re.sub(r"```json\n?|```", "", full_out).strip()
        try:
            out_dict = json.loads(cleaned_output)
        except json.JSONDecodeError as e:
            log.error(
                "JSON decoding of Gemini output failed", json_error=str(e)
            )
            return False, dict(), ""
        return True, out_dict, full_out

    def upload_video(self, video_path: str) -> typing.Tuple[bool, str]:
        mime_type = (
            mimetypes.guess_type(video_path)[0] or "application/octet-stream"
        )
        num_bytes = os.path.getsize(video_path)
        display_name = os.path.basename(video_path)

        data = {"file": {"display_name": display_name}}
        headers = {
            "X-Goog-Upload-Protocol": "resumable",
            "X-Goog-Upload-Command": "start",
            "X-Goog-Upload-Header-Content-Length": str(num_bytes),
            "X-Goog-Upload-Header-Content-Type": mime_type,
            "Content-Type": "application/json",
        }
        response = requests.post(
            f"{self.base_url}/upload/v1beta/files?key={self.api_key}",
            headers=headers,
            json=data,
        )

        upload_url = response.headers.get("X-Goog-Upload-URL")
        if not upload_url:
            log.error("Failed to get upload URL")
            return None, None

        with open(video_path, "rb") as video_file:
            upload_headers = {
                "Content-Length": str(num_bytes),
                "X-Goog-Upload-Offset": "0",
                "X-Goog-Upload-Command": "upload, finalize",
            }
            upload_response = requests.post(
                upload_url, headers=upload_headers, data=video_file
            )

        file_info = upload_response.json()
        file_uri = file_info.get("file", {}).get("uri")
        file_name = file_info.get("file", {}).get("name")
        state = file_info.get("file", {}).get("state")

        while state == "PROCESSING":
            time.sleep(1)
            check_response = requests.get(
                f"{self.base_url}/v1beta/{file_name}?key={self.api_key}"
            )

            state = check_response.json().get("state")

        if state != "ACTIVE":
            log.error("Gemini video processing failed or is incomplete.")
            return False, None
        return True, file_uri

    def send_request_with_video(
        self,
        text: str,
        video_path: str,
        use_video_uri: bool = False,
    ) -> typing.Tuple[bool, typing.Dict[str, str], str]:
        file_uri, video_bytes = None, None
        if use_video_uri:
            upload_success, file_uri = self.upload_video(video_path)
            if not upload_success:
                return False, dict(), ""
        else:
            with open(video_path, "rb") as video_file:
                video_bytes = base64.b64encode(video_file.read()).decode(
                    "utf-8"
                )
        request_success, out_dict, full_out = self.send_request(
            text, file_uri, video_bytes
        )
        if not request_success:
            return False, dict(), ""
        return True, out_dict, full_out

    def analyze_location_alarm(
        self,
        previous_analysis: typing.List[typing.Dict[str, str]],
        sop: typing.Optional[str],
        location_timezone: str,
        temperature: float = 0,
    ) -> typing.Tuple[typing.Optional[str], typing.Optional[str]]:
        content_parts = []

        content_parts.append(
            {
                "text": (
                    "This is a site that is being monitored by a remote security operator. "
                    f"The local timezone for the site is {location_timezone}. "
                    "Following are the details of raw video events detected by the cameras on the site along with an explanation of what is happening in those videos. "
                )
            }
        )
        content_parts.extend(previous_analysis)

        content_parts.append(
            {
                "text": (
                    f"This is the Standard Operating Procedure the operator has to follow: {sop} "
                    "Based on this information, is there any unauthorized or suspicious activity happening at the site? "
                    "Do not assume that any person/vehicle is authorized unless you are absolutely certain. "
                    "Provide an overall summary and a recommendation based on all the events on whether to Resolve or Escalate the activity. "
                    "If any of the video events are suspicious, the activity should be escalated. "
                    "The recommendation should be Resolve if there is nothing suspicious and no further action is needed, \
                or Escalate if the operator should take further action. "
                    "Respond in json format with keys 'summary' and 'recommendation' where recommendation is either Resolve or Escalate. "
                )
            }
        )
        content_request = {
            "generationConfig": {"temperature": temperature},
            "contents": [{"parts": content_parts}],
        }
        generate_headers = {"Content-Type": "application/json"}
        generate_response = requests.post(
            f"{self.base_url}/v1beta/models/gemini-2.0-flash:generateContent?key={self.api_key}",
            headers=generate_headers,
            json=content_request,
            timeout=self.timeout,
        )

        response_data = generate_response.json()
        out = [
            part["text"]
            for candidate in response_data.get("candidates", [])
            for part in candidate.get("content", {}).get("parts", [])
        ]
        full_out = "\n".join(out)
        # Strip leading/trailing spaces and remove markdown markers
        cleaned_output = re.sub(r"```json\n?|```", "", full_out).strip()
        try:
            out_dict = json.loads(cleaned_output)
        except json.JSONDecodeError as e:
            log.error(
                "JSON decoding of Gemini output failed", json_error=str(e)
            )
            return None, None
        loc_summary = out_dict.get("summary")
        loc_reco = out_dict.get("recommendation")
        log.info(
            "Gemini analysis of location alarm complete",
            full_output=full_out,
            summary=loc_summary,
            loc_reco=loc_reco,
        )

        return loc_summary, loc_reco

    def analyze_alarm(
        self,
        video_path: str,
        video_times: typing.Tuple[datetime, datetime],
        sop: typing.Optional[str],
        camera_name: str,
        location_timezone: str,
        send_inline_video: bool = False,
        temperature: float = 0,
    ) -> typing.Tuple[typing.Optional[str], typing.Optional[str]]:
        start_time = time.time()

        if send_inline_video:
            with open(video_path, "rb") as f:
                video_bytes = f.read()
                video_base64 = base64.b64encode(video_bytes).decode("utf-8")
                video_part = {
                    "inline_data": {
                        "mime_type": "video/mp4",
                        "data": video_base64,
                    }
                }
        else:
            mime_type = (
                mimetypes.guess_type(video_path)[0]
                or "application/octet-stream"
            )
            num_bytes = os.path.getsize(video_path)
            display_name = os.path.basename(video_path)

            # Step 1: Start resumable upload request
            data = {"file": {"display_name": display_name}}
            headers = {
                "X-Goog-Upload-Protocol": "resumable",
                "X-Goog-Upload-Command": "start",
                "X-Goog-Upload-Header-Content-Length": str(num_bytes),
                "X-Goog-Upload-Header-Content-Type": mime_type,
                "Content-Type": "application/json",
            }
            response = requests.post(
                f"{self.base_url}/upload/v1beta/files?key={self.api_key}",
                headers=headers,
                json=data,
            )

            # Extract upload URL
            upload_url = response.headers.get("X-Goog-Upload-URL")
            if not upload_url:
                log.error("Failed to get upload URL")
                return None, None

            # Step 2: Upload the video
            with open(video_path, "rb") as video_file:
                upload_headers = {
                    "Content-Length": str(num_bytes),
                    "X-Goog-Upload-Offset": "0",
                    "X-Goog-Upload-Command": "upload, finalize",
                }
                upload_response = requests.post(
                    upload_url, headers=upload_headers, data=video_file
                )

            file_info = upload_response.json()
            file_uri = file_info.get("file", {}).get("uri")
            file_name = file_info.get("file", {}).get("name")
            state = file_info.get("file", {}).get("state")

            # Step 3: Ensure the state of the video is 'ACTIVE'
            retry_number = 0
            while state == "PROCESSING" and retry_number < 5:
                time.sleep(1)
                check_response = requests.get(
                    f"{self.base_url}/v1beta/{file_name}?key={self.api_key}"
                )
                retry_number += 1
                state = check_response.json().get("state")

            if state != "ACTIVE":
                log.error(
                    "Gemini video processing failed or is incomplete.",
                    retry_number=retry_number,
                )
                return None, None

            log.info(
                "Time to upload video to Gemini",
                upload_time=time.time() - start_time,
            )
            video_part = {
                "file_data": {
                    "mime_type": "video/mp4",
                    "file_uri": file_uri,
                }
            }

        # Step 4: Generate content using the uploaded video
        content_request = {
            "generationConfig": {"temperature": temperature},
            "contents": [
                {
                    "parts": [
                        {
                            "text": (
                                "This is a site that is being monitored by a remote security operator. "
                                f"The local timezone for the site is {location_timezone}. "
                                f"This is the Standard Operating Procedure the operator has to follow: {sop} "
                                f"Here is a video clip from camera {camera_name} at the site. "
                                f"The video start time is {video_times[0]} UTC and end time is {video_times[1]} UTC. "
                                "Is there anything suspicious happening in the video that is mentioned in the SOP? "
                                "Is there any unauthorized or suspicious activity happening in the video that is mentioned in the Standard Operating Procedure? "
                                "Do not assume that any person/vehicle is authorized unless you are absolutely certain. "
                                "If there are any suspicious persons or moving vehicles, describe their appearance, behavior, and any other relevant details. "
                                "Analyze the video and provide a recommendation to the operator on whether to Resolve or Escalate the activity. "
                                "The recommendation should be Resolve if there is nothing suspicious and no further action is needed, \
                                or Escalate if the operator should take further action. "
                                "Respond in json format with keys 'explanation' and 'recommendation' where recommendation is either Resolve or Escalate. "
                            )
                        },
                        video_part,
                    ]
                }
            ],
        }
        generate_headers = {"Content-Type": "application/json"}
        generate_response = requests.post(
            f"{self.base_url}/v1beta/models/gemini-2.0-flash:generateContent?key={self.api_key}",
            headers=generate_headers,
            json=content_request,
            timeout=self.timeout,
        )

        # Step 5: Parse and send the response
        response_data = generate_response.json()
        log.info(
            "Time taken for Gemini analysis",
            llm_analysis_time=time.time() - start_time,
        )
        out = [
            part["text"]
            for candidate in response_data.get("candidates", [])
            for part in candidate.get("content", {}).get("parts", [])
        ]
        full_out = "\n".join(out)
        # Strip leading/trailing spaces and remove markdown markers
        cleaned_output = re.sub(r"```json\n?|```", "", full_out).strip()
        try:
            out_dict = json.loads(cleaned_output)
        except json.JSONDecodeError as e:
            log.error(
                "JSON decoding of Gemini output failed", json_error=str(e)
            )
            return None, None
        analysis = out_dict.get("explanation")
        recommendation = out_dict.get("recommendation")
        log.info(
            "Gemini analysis complete",
            llm_analysis_time=time.time() - start_time,
            full_output=full_out,
            analysis=analysis,
            recommendation=recommendation,
        )

        return analysis, recommendation
