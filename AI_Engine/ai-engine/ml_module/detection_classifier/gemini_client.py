# # not needed right now
# import asyncio
# import json
# import os
# import time
# import uuid
# from typing import AsyncGenerator, Dict

# import uvicorn
# from dotenv import load_dotenv
# from fastapi import FastAPI, WebSocket, WebSocketDisconnect
# from fastapi.middleware.cors import CORSMiddleware
# from google import genai

# # Load environment variables
# load_dotenv()
# GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")


# class ClientSession:
#     """Manages a persistent Gemini session for a single client"""

#     def __init__(self, client_id: str, api_key: str):
#         self.client_id = client_id
#         self.client = genai.Client(
#             api_key=api_key, http_options={"api_version": "v1alpha"}
#         )
#         self.model = "gemini-2.0-flash-exp"
#         self.config = {"response_modalities": ["TEXT"]}
#         self.session = None
#         self.is_connected = False

#     async def connect(self):
#         """Create a new Gemini session"""
#         if not self.is_connected:
#             self.session = await self.client.aio.live.connect(
#                 model=self.model, config=self.config
#             )
#             self.is_connected = True
#             print(f"Gemini session started for client {self.client_id}")

#     async def disconnect(self):
#         """Close the Gemini session"""
#         if self.is_connected and self.session:
#             await self.session.close()
#             self.is_connected = False
#             self.session = None
#             print(f"Gemini session closed for client {self.client_id}")

#     async def send_message(self, message: str) -> AsyncGenerator[str, None]:
#         """Send message to Gemini and yield response chunks"""
#         if not self.is_connected:
#             await self.connect()

#         try:
#             await self.session.send(input=message, end_of_turn=True)

#             async for response in self.session.receive():
#                 if response.text is not None:
#                     yield response.text
#         except Exception as e:
#             print(
#                 f"Error in Gemini session for client {self.client_id}: {str(e)}"
#             )
#             # Attempt to reconnect on error
#             await self.disconnect()
#             await self.connect()
#             yield f"Error: {str(e)}. Session has been reset."


# class ConnectionManager:
#     def __init__(self, api_key: str):
#         self.api_key = api_key
#         self.active_connections: Dict[str, WebSocket] = {}
#         self.client_sessions: Dict[str, ClientSession] = {}

#     async def connect(self, websocket: WebSocket, client_id: str):
#         """Connect a new client WebSocket and create a Gemini session"""
#         await websocket.accept()
#         self.active_connections[client_id] = websocket

#         # Create new client session
#         if client_id not in self.client_sessions:
#             self.client_sessions[client_id] = ClientSession(
#                 client_id, self.api_key
#             )
#             await self.client_sessions[client_id].connect()

#         print(
#             f"Client {client_id} connected. Total connections: {len(self.active_connections)}"
#         )

#     async def disconnect(self, client_id: str):
#         """Disconnect client WebSocket and close Gemini session"""
#         if client_id in self.active_connections:
#             del self.active_connections[client_id]

#         if client_id in self.client_sessions:
#             await self.client_sessions[client_id].disconnect()
#             del self.client_sessions[client_id]

#         print(
#             f"Client {client_id} disconnected. Total connections: {len(self.active_connections)}"
#         )

#     async def send_message(
#         self, client_id: str, message: str, is_stream_complete: bool = False
#     ):
#         """Send message to client WebSocket"""
#         if client_id in self.active_connections:
#             response_data = {
#                 "message": message,
#                 "timestamp": time.time(),
#                 "is_stream_complete": is_stream_complete,
#             }
#             await self.active_connections[client_id].send_text(
#                 json.dumps(response_data)
#             )

#     async def process_message(self, client_id: str, message: str):
#         """Process user message and generate AI response using persistent session"""
#         if client_id not in self.client_sessions:
#             error_message = "Session not found. Please reconnect."
#             await self.send_message(
#                 client_id, error_message, is_stream_complete=True
#             )
#             return

#         try:
#             async for response_chunk in self.client_sessions[
#                 client_id
#             ].send_message(message):
#                 # Send each chunk as it arrives
#                 await self.send_message(client_id, response_chunk)

#             # Signal that the stream is complete
#             await self.send_message(client_id, "", is_stream_complete=True)

#         except Exception as e:
#             error_message = f"Error generating response: {str(e)}"
#             await self.send_message(
#                 client_id, error_message, is_stream_complete=True
#             )


# app = FastAPI()

# # Configure CORS
# app.add_middleware(
#     CORSMiddleware,
#     allow_origins=["*"],  # For production, specify your frontend URL
#     allow_credentials=True,
#     allow_methods=["*"],
#     allow_headers=["*"],
# )

# # Initialize connection manager with API key
# manager = ConnectionManager(api_key=GEMINI_API_KEY)


# @app.get("/")
# async def get_status():
#     """Endpoint to check API status"""
#     return {
#         "status": "online",
#         "active_connections": len(manager.active_connections),
#         "active_sessions": len(manager.client_sessions),
#     }


# @app.websocket("/ws/{client_id}")
# async def websocket_endpoint(websocket: WebSocket, client_id: str):
#     """WebSocket endpoint for real-time chat"""
#     # Generate a unique ID if not provided
#     if client_id == "new":
#         client_id = str(uuid.uuid4())

#     # Connect the websocket and create Gemini session
#     await manager.connect(websocket, client_id)

#     try:
#         while True:
#             # Receive message from client
#             data = await websocket.receive_text()
#             try:
#                 # Parse message data
#                 message_data = json.loads(data)
#                 user_message = message_data.get("message", "")

#                 # Add timestamp and send receipt confirmation
#                 receipt_data = {
#                     "client_id": client_id,
#                     "message": user_message,
#                     "timestamp": time.time(),
#                     "status": "received",
#                 }
#                 await websocket.send_text(json.dumps(receipt_data))

#                 # Process message using persistent session
#                 asyncio.create_task(
#                     manager.process_message(client_id, user_message)
#                 )

#             except json.JSONDecodeError:
#                 await websocket.send_text(
#                     json.dumps({"error": "Invalid JSON format"})
#                 )

#     except WebSocketDisconnect:
#         # Clean up session when client disconnects
#         await manager.disconnect(client_id)


# # Graceful shutdown handler
# @app.on_event("shutdown")
# async def shutdown_event():
#     """Close all Gemini sessions when server shuts down"""
#     for client_id in list(manager.client_sessions.keys()):
#         await manager.disconnect(client_id)


# if __name__ == "__main__":
#     uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
